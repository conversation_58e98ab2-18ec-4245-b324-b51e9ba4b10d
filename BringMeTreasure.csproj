<Project Sdk="Godot.NET.Sdk/4.4.1">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <EnableDynamicLoading>true</EnableDynamicLoading>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="FluentAssertions" Version="7.2.0" />
        <PackageReference Include="NUnit" Version="3.14.0" />
        <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
        <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
        <PackageReference Include="System.Reactive" Version="6.0.1" />
        <PackageReference Include="System.Reactive.Linq" Version="6.0.1" />
        <PackageReference Include="System.Reactive.Interfaces" Version="6.0.1" />
    </ItemGroup>
    <ItemGroup>
        <Folder Include=".godot\mono\" />
        <Folder Include="Application\Scripts\Characters\" />
    </ItemGroup>
    <ItemGroup>
      <Content Include="Application\Stops\SlimeStop_1_1.tres" />
      <Content Include="Application\translation.csv" />
      <Content Include="Tests\.gdignore" />
    </ItemGroup>


</Project>


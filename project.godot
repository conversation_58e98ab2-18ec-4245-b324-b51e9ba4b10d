; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="BringMeTreasure"
run/main_scene="uid://b76haa6t2dptd"
config/features=PackedStringArray("4.4", "C#", "Mobile")
config/icon="res://icon.svg"

[autoload]

GameStateManager="*res://Application/Scripts/Saves/GameStateManager.cs"
SceneManager="*res://Application/Scripts/SceneManager.cs"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/stretch/mode="viewport"
window/vsync/vsync_mode=0

[dotnet]

project/assembly_name="BringMeTreasure"

[internationalization]

locale/translations=PackedStringArray("res://Application/translation.en.translation", "res://Application/translation.jp.translation")

[rendering]

gl_compatibility/fallback_to_native=false
renderer/rendering_method="mobile"
textures/vram_compression/import_etc2_astc=true

using System.Numerics;
using BringMeTreasure.Models;
using BringMeTreasure.SimpleStuff;
using FluentAssertions;
using NSubstitute;

namespace DefaultNamespace;

public class LootTableTests
{
    [Test]
    [TestCase(0.1f,  0)]
    [TestCase(0.3f,  1)]
    [TestCase(0.5f,  2)]
    [TestCase(0.65f, 3)]
    [TestCase(0.8f,  4)]
    [TestCase(0.95f, 5)]
    public void Test(float roll, int expected)
    {
        Console.WriteLine($"LootTableTests.Test: roll={roll}, expected={expected}");
        IRng rng = Substitute.For<IRng>();
        rng.Randf().Returns(roll); // Simulate a roll that would yield a common item
        rng.RandfRange(Arg.Any<float>(), Arg.Any<float>()).Returns(arg => arg.ArgAt<float>(0));
        // rng.RandfRange(Arg.Any<int>(), Arg.Any<int>()).Returns(arg => arg.ArgAt<int>(0));
        rng.RandiRange(Arg.Any<int>(),   Arg.Any<int>()).Returns(arg => arg.ArgAt<int>(0));
        LootTable.LootTableRowInstance[] items =
        [
            new(SupplyItemType.Wood, new Vector2(1, 1), Rarity.Common),
            new(SupplyItemType.Wood, new Vector2(2, 2), Rarity.Uncommon),
            new(SupplyItemType.Wood, new Vector2(3, 3), Rarity.Rare),
            new(SupplyItemType.Wood, new Vector2(4, 4), Rarity.VeryRare),
            new(SupplyItemType.Wood, new Vector2(5, 5), Rarity.Legendary)
        ];

        SupplyItemType[] rolledItems = LootTable.StaticRoll(items, rng, 1);
        rolledItems.Should().HaveCount(expected,
            "because the roll should yield the expected number of items based on rarity thresholds");
    }
}
<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="NSubstitute" Version="5.3.0"/>
        <PackageReference Include="FluentAssertions" Version="7.2.0"/>
        <PackageReference Include="NUnit" Version="3.14.0"/>
        <PackageReference Include="NUnit.Analyzers" Version="3.9.0"/>
        <PackageReference Include="NUnit3TestAdapter" Version="4.5.0"/>
        <PackageReference Include="System.Reactive" Version="6.0.1"/>
        <PackageReference Include="System.Reactive.Linq" Version="6.0.1"/>
        <PackageReference Include="System.Reactive.Interfaces" Version="6.0.1"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="NUnit.Framework"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BringMeTreasure.csproj"/>
    </ItemGroup>


</Project>

using System.Reflection;
using BringMeTreasure;
using BringMeTreasure.Abilities;
using FluentAssertions;

namespace System;

public class SerializationTests
{
    [Test]
    public void ClassesCanSerializeAndDeserializeTheirDataProperly()
    {
        SwordAttack swordAttack = new("Sword Attack", "A powerful sword attack", 3, 10);
        PrivateFieldEquals(swordAttack, SerializeAndDeserialize(swordAttack)).Should().BeTrue();
        Fortify fortify = new("Fortify", "Increases armor", 2, 5);
        PrivateFieldEquals(fortify, SerializeAndDeserialize(fortify)).Should().BeTrue();
    }


    private T SerializeAndDeserialize<T>(T obj)
    {
        // Serialize the object to JSON
        string json = Newtonsoft.Json.JsonConvert.SerializeObject(obj, GameState.GetJsonSettings());
        Console.WriteLine($"Serialized JSON: {json}");
        // Deserialize the JSON back to an object of type T
        T deserializedObj = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json, GameState.GetJsonSettings());

        return deserializedObj;
    }

    public static bool PrivateFieldEquals<T>(T obj1, T obj2)
    {
        if (ReferenceEquals(obj1, obj2)) return true;
        if (obj1 == null || obj2 == null) return false;

        Type type = typeof(T);
        var fields = type.GetFields(BindingFlags.Instance | BindingFlags.NonPublic);

        foreach (var field in fields)
        {
            var val1 = field.GetValue(obj1);
            var val2 = field.GetValue(obj2);
            
            Console.WriteLine($"Comparing field {field.Name}: {val1} vs {val2}");

            if (!Equals(val1, val2))
                return false;
        }

        return true;
    }
}
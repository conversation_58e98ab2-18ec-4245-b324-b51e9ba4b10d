using BringMeTreasure.Application.Items.Relics;
using BringMeTreasure.GodotAsync;
using Godot;
using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;

namespace BringMeTreasure.StartUp;

public partial class StartUp : Control
{
    public override void _Ready()
    {
        ReadyTask().Forget();
    }

    public async Task ReadyTask()
    {
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        ISave save = GameStateManager.Instance;
        if (save.GetParty().Length == 0)
        {
            save.SetParty([
                Raider.Create(),
                Raider.Create(),
                Raider.Create(),
                Raider.Create(),
            ]);
        }

        if (save.GetInventory().GetCraftedItems().Count == 0)
        {
            save.GetInventory().AddItem(new CraftedItem("sword", "a sword", "res://Application/Scenes/Battle/armor.png",
                ItemType.Weapon, [new BasicAttackRelic()]));
        }

        if (!save.<PERSON><PERSON>ey(nameof(ISave.Keys.CurrentRoute)) ||
            save.GetKey<Routes>(nameof(ISave.Keys.CurrentRoute)) == Routes.None)
        {
            SceneManager.Instance.ChangeScene(ISceneManager.SceneType.Town);
        }
        else
        {
            if (save.HasKey(nameof(ISave.Keys.Arrived)) && !save.GetKey<bool>(nameof(ISave.Keys.Arrived)))
            {
                SceneManager.Instance.ChangeScene(ISceneManager.SceneType.Battle);
                return;
            }


            SceneManager.Instance.ChangeScene(ISceneManager.SceneType.Travel);
        }
    }
}
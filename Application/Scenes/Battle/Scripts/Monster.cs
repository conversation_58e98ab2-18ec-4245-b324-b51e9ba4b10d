using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Battle;

[GlobalClass]
public partial class Monster : Creature, IMonster
{
    private MonsterController controller;
    public new IMonsterController Controller => controller ??= new MonsterController(this);

    public async Task FadeOut(float f)
    {
        Tween fadeOutTween = CreateTween();
        Color finalColor   = textureButton.Modulate;
        finalColor.A = 0f;
        fadeOutTween.TweenProperty(textureButton, "modulate", finalColor, f);
        await Task.Delay(TimeSpan.FromSeconds(f));
    }
}

public class MonsterController(IMonster implementation) : CreatureController(implementation), IMonsterController
{
    private IMonsterData monsterData;
    private Subject<bool> reducedToZeroSubject = new();

    public void Init(IMonsterData monster)
    {
        base.Init();
        monsterData = monster;
        implementation.PlayIdleAnimation();
        healthPoints   = monster.GetHealthPoints();
        armorPoints    = monster.GetArmorPoints();
        positionPoints = monster.GetPositionPoints();
        UpdateDisplay();
        implementation.SetTexture(monster.GetTexture());
    }

    public IBattleCharacterController GetTarget(List<IBattleCharacterController> characters)
    {
        //todo make this more intelligent
        return characters.Random();
    }

    public async Task RunAttack(IBattleCharacterController character)
    {
        if (healthPoints == 0)
        {
            return;
        }

        //select which attack to run
        IMonsterAbility attack = monsterData.GetAttack();

        if (attack.IsRanged())
        {
            await monsterData.PlayRangedAttackAnimation(implementation);
        }
        else
        {
            throw new NotImplementedException();
        }

        implementation.StopAnimation();
        await attack.RunAttackAnimation(GetCenterPosition(), character.GetCenterPosition());
        implementation.PlayIdleAnimation();
        ApplyDamage(character, attack);


        await Task.Delay(TimeSpan.FromSeconds(1));
    }

    public static void ApplyDamage(IBattleCharacterController character, IMonsterAbility attack)
    {
        character.TakeArmorDamage(attack.GetArmorOnlyDamage());

        // damage armor first, then remaining damage to health
        int armorDamageTaken = character.TakeArmorDamage(attack.GetDamage());
        character.TakeHealthDamage(attack.GetDamage() - armorDamageTaken);

        character.TakePositionDamage(attack.GetPositionDamage());
    }

    public IObservable<bool> ReducedToZeroAsObservable()
    {
        return reducedToZeroSubject.AsObservable();
    }

    public void QueueFree()
    {
        Dispose();
        implementation.QueueFree();
    }

    public IMonsterData GetMonsterData()
    {
        return monsterData;
    }

    public Task FadeOut()
    {
        return implementation.FadeOut(1f);
    }

    protected override void ReducedToZero()
    {
        base.ReducedToZero();
        implementation.SetInteractive(false);
        implementation.StopAnimation();
        reducedToZeroSubject.OnNext(true);
    }

    protected override void UndoReducedToZero()
    {
        base.UndoReducedToZero();
        implementation.SetInteractive(true);
        implementation.PlayIdleAnimation();
        reducedToZeroSubject.OnNext(false);
    }
}

public interface IMonsterController : ICreatureController
{
    void Init(IMonsterData monster);
    IBattleCharacterController GetTarget(List<IBattleCharacterController> battleCharacters);
    Task RunAttack(IBattleCharacterController character);

    public IObservable<bool> ReducedToZeroAsObservable();
    void QueueFree();
    IMonsterData GetMonsterData();
    Task FadeOut();
}

public interface IMonster : ICreature
{
    void QueueFree();
    Task FadeOut(float f);
}
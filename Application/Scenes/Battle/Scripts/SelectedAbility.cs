using BringMeTreasure.Models;

namespace BringMeTreasure.Battle;

public class SelectedCombatAbility(ICombatAbility combatAbility, IBattleCharacterController caster): ISelectedCombatAbility
{
    public string GetName()
    {
        return combatAbility.GetName();
    }

    public string GetDescription()
    {
        return combatAbility.GetDescription();
    }

    public int GetUses()
    {
        return combatAbility.GetUses();
    }

    public ICombatAbilityEffect[] GetEffects()
    {
        return combatAbility.GetEffects();
    }

    public bool GetIsCostsAction()
    {
        return combatAbility.GetIsCostsAction();
    }

    public RegainType GetRegainType()
    {
        return combatAbility.GetRegainType();
    }

    public void Use()
    {
        combatAbility.Use();
    }

    public IBattleCharacterController GetCaster()
    {
        return caster;
    }
}
public interface ISelectedCombatAbility: ICombatAbility
{
    public IBattleCharacterController GetCaster();
}
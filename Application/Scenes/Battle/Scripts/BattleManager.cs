using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using BringMeTreasure.Application;
using BringMeTreasure.Extensions;
using BringMeTreasure.GodotAsync;
using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;
using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Battle;

public partial class BattleManager : Control, IBattleManager
{
    [Export] BattleCharacter defaultCharacter;
    [Export] Monster defaultMonster;
    [Export] AbilitySelector abilitySelector;
    [Export] TextureButton closeButton;
    [Export] AbilityDialog abilityDialog;
    [Export] BaseButton undoButton;
    [Export] BaseButton nextButton;
    [Export] DrawArrow drawArrow;

    [Export] Control originalStop;
    [Export] Control blank;
    [Export] Control partyIcon;
    [Export] Control destinationIcon;

    [Export] BattleLoot originalBattleLoot;


    private List<Control> stops = new();
    private BattleManagerController controller;
    public IBattleManagerController Controller => controller ??= new BattleManagerController(this);

    public override void _Ready()
    {
        GD.Randomize();
        base._Ready();
        Controller.AddTo(this);
        Controller.Init();
        // quality of life
        defaultCharacter.Visible   = false;
        defaultMonster.Visible     = false;
        drawArrow.Visible          = false;
        originalBattleLoot.Visible = false;
    }

    public override void _UnhandledInput(InputEvent @event)
    {
        base._UnhandledInput(@event);

        if (@event is InputEventKey eventKey)
        {
            if (eventKey.Pressed && eventKey.Keycode == Key.K)
            {
                controller.DebugDamageAllMonsters();
            }
        }
        
    }

    public async Task WaitForProcessFrame()
    {
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
    }

    public void SetNextButtonVisible(bool b)
    {
        nextButton.SetVisible(b);
    }

    public IObservable<Unit> OnNextButtonClickedAsObservable()
    {
        return nextButton.OnButtonPressedAsObservable();
    }

    public IDrawArrow MakeDrawArrow(Node2D start, Node2D end, Color color)
    {
        DrawArrow newArrow = (DrawArrow) drawArrow.Duplicate();

        drawArrow.FindParent("*").AddChild(newArrow);
        newArrow.PositionArrow(start, end, color);
        newArrow.Visible = true; // If you want to ensure it's visible/active
        return newArrow;
    }

    public IDisposable ShowClear()
    {
        return LoadingScreen.ShowClear();
    }

    public void SetPartyPosition(int stopIndex)
    {
        Control stop;
        if (stopIndex == -1)
        {
            stop = blank;
        }
        else if (stopIndex >= stops.Count)
        {
            stop = destinationIcon;
        }
        else
        {
            stop = stops[stopIndex];
        }

        Vector2 stopMiddle = stop.GlobalPosition + new Vector2(0, stop.Size.Y / 2);
        partyIcon.GlobalPosition = stopMiddle + new Vector2(0, -partyIcon.Size.Y / 2);
    }

    public void CreateStops(int numberOfStops)
    {
        originalStop.Visible = false; // Hide the original stop control
        for (int i = 0; i < numberOfStops; i++)
        {
            Control stop = originalStop.Duplicate() as Control;
            stop.Name    = $"Stop_{i}";
            stop.Visible = true;
            originalStop.GetParentControl().AddChild(stop);
            stops.Add(stop);
        }

        originalStop.GetParentControl().MoveChild(destinationIcon, numberOfStops + 2); // +1 for blank
    }

    public IBattleLootController DisplayLootOn(IMonsterController monster)
    {
        BattleLoot copy = (BattleLoot) originalBattleLoot.Duplicate();
        originalBattleLoot.GetParent().AddChild(copy);
        copy.GlobalPosition = monster.GetCenterPosition().GlobalPosition + (copy.Size / 2);
        return copy.Controller;
    }

    public IAbilitySelectorController GetAbilitySelector()
    {
        return abilitySelector.Controller;
    }

    public void SetCloseButtonVisibility(bool b)
    {
        closeButton.Visible = b;
    }

    public IBattleCharacterController CreateBattleCharacter()
    {
        BattleCharacter newCharacter = (BattleCharacter) defaultCharacter.Duplicate();
        defaultCharacter.FindParent("*").AddChild(newCharacter);
        newCharacter.Visible = true; // If you want to ensure it's visible/active
        return newCharacter.Controller;
    }

    public IMonsterController CreateMonster()
    {
        Monster newCharacter = (Monster) defaultMonster.Duplicate();
        defaultMonster.FindParent("*").AddChild(newCharacter);
        newCharacter.Visible = true; // If you want to ensure it's visible/active
        return newCharacter.Controller;
    }

    public IAbilityDialogController GetAbilityDialogController()
    {
        return abilityDialog.Controller;
    }

    public IObservable<Unit> OnBackButtonAsObservable()
    {
        return undoButton.OnButtonPressedAsObservable()
            .Where(_ => undoButton.Visible); // Only trigger if the button is visible
    }

    public IObservable<Unit> OnCloseButtonClickedAsObservable()
    {
        return closeButton.OnButtonPressedAsObservable();
    }

    public ICharacterData[] GetTeam()
    {
        return GameStateManager.Instance.GetParty();
    }

    public List<IMonsterData> GetMonsters()
    {
        MonsterData m = ResourceLoader.Load<MonsterData>("res://Application/Monsters/Slime/Slime.tres");

        return new List<IMonsterData>()
        {
            m,
            m,
            m,
            m,
        };
    }

    public Vector2 GetPartyGlobalCenterPosition()
    {
        return partyIcon.GlobalPosition + (partyIcon.Size / 2);
    }
}

public class BattleManagerController(IBattleManager implementation) : IBattleManagerController
{
    private CompositeDisposable disposables = new();
    private CompositeDisposable disposeOnRoundEnd = new();
    private CompositeDisposable disposeOnBattleEnd = new();
    private List<IBattleCharacterController> battleCharacters = new();
    private List<IMonsterController> monsters = new();
    private List<IUndo> undoList = new();
    private Dictionary<IMonsterController, IDrawArrow> drawArrows = new();
    private Dictionary<IMonsterController, IBattleCharacterController> targetPairings = new();

    private StopManagerController stopManagerController;

    public void Dispose()
    {
        disposables.Dispose();
    }

    public void Init()
    {
        SetTheTeam(implementation.GetTeam());
        stopManagerController = new StopManagerController(implementation, new StopManager(), this);
        stopManagerController.Start();
        ListenForCloseButtonClick();
        ListenForBackButton();
        SetUpTurnEndButton();
        implementation.SetCloseButtonVisibility(false);
    }

    public IMonsterController[] GetMonsters()
    {
        return monsters.ToArray();
    }

    public async Task ReadyNewBattle()
    {
        await implementation.WaitForProcessFrame();
        Assert.IsNotEmpty(monsters);
        disposeOnBattleEnd?.Dispose();
        disposeOnBattleEnd = new CompositeDisposable();
        disposeOnBattleEnd.AddTo(disposables);
        ListenForAbilitySelected();
        await OnTeamTurnEnd_1();
        StartRound_3().Forget();
        ListenForAllMonstersReducedToZero();
    }

    public void SetUpTurnEndButton()
    {
        //1. button only shows when all actions are spent
        battleCharacters.Select(b => b.OnActionChangedAsObservable())
            .Merge()
            .Subscribe(_ =>
            {
                bool allSpent = battleCharacters.All(c => !c.GetHasAction());
                implementation.SetNextButtonVisible(allSpent);
            })
            .AddTo(disposables);
        //2. subscribe to button click
        implementation.OnNextButtonClickedAsObservable()
            .Subscribe(_ => { ProgressRound().Forget(); })
            .AddTo(disposables);
    }

    private async Task ProgressRound()
    {
        await OnTeamTurnEnd_1();
        await RunMonsterActions_2();
        await StartRound_3();
    }

    private async Task OnTeamTurnEnd_1()
    {
        undoList.Clear();
        disposeOnRoundEnd.Dispose();
        disposeOnRoundEnd = new CompositeDisposable();
        disposeOnRoundEnd.AddTo(disposables);
    }

    private async Task RunMonsterActions_2()
    {
        foreach (var kv in targetPairings)
        {
            IMonsterController         monster   = kv.Key;
            IBattleCharacterController character = kv.Value;
            await monster.RunAttack(character);
        }
    }

    private async Task StartRound_3()
    {
        battleCharacters.ForEach(c => c.SetHasActionAndUi(true));
        // 1. make parings of which monster attacks which character
        targetPairings.Clear();
        monsters
            .Where(m => m.GetHealthPoints() > 0) // Only consider monsters that are alive
            .ForEach(m => { targetPairings.Add(m, m.GetTarget(battleCharacters)); });
        await DrawArrowsFromMonstersToCharacters(targetPairings);
    }

    private async Task DrawArrowsFromMonstersToCharacters(
        Dictionary<IMonsterController, IBattleCharacterController> targetPairings)
    {
        using (implementation.ShowClear())
        {
            await implementation.WaitForProcessFrame();
            foreach (var pair in targetPairings.Shuffle())
            {
                await Task.Delay(TimeSpan.FromSeconds(.5));
                IMonsterController         monster   = pair.Key;
                IBattleCharacterController character = pair.Value;
                IDrawArrow drawArrow =
                    implementation.MakeDrawArrow(character.GetCenterPosition(), monster.GetCenterPosition(),
                        Colors.Red);
                drawArrows.Add(monster, drawArrow);
            }
        }

        await Task.Delay(TimeSpan.FromSeconds(1));
        drawArrows.Values.ForEach(d => { d.Fade(1f).AddTo(disposeOnRoundEnd); });
        await Task.Delay(TimeSpan.FromSeconds(1));
        drawArrows.Values.ForEach(d => { d.Hide(); });
        Disposable.Create(() =>
        {
            drawArrows.Values.ForEach(d => d.Destroy());
            drawArrows.Clear();
        }).AddTo(disposeOnRoundEnd);
        ListenForMonsterHoverToShowDrawArrow(drawArrows);
    }

    private void ListenForMonsterHoverToShowDrawArrow(Dictionary<IMonsterController, IDrawArrow> monsterArrowMap)
    {
        monsterArrowMap.ForEach(kv =>
        {
            kv.Key.OnHoveredAsObservable()
                .Where(_ => implementation.GetAbilitySelector().IsVisible() == false)
                .Subscribe(_ => { kv.Value.Show(); })
                .AddTo(disposeOnRoundEnd);
            kv.Key.OnUnhoveredAsObservable()
                .Subscribe(_ => { kv.Value.Hide(); })
                .AddTo(disposeOnRoundEnd);
        });
    }

    private void ListenForBackButton()
    {
        implementation.OnBackButtonAsObservable()
            .Select(n =>
            {
                // GD.Print("");
                // GD.Print(undoList.Count, " actions to undo");
                return n;
            })
            .Where(_ => undoList.Count != 0)
            .Subscribe(_ =>
            {
                do
                {
                    // GD.Print("Undoing ", undoList.Last().GetUndoDescription(), " immediately: ",
                    // undoList.Last().GetIsUndoImmediately());
                    undoList.Last().UndoEffect();
                    undoList.RemoveAt(undoList.Count - 1);
                } while (undoList.Count != 0 && undoList.Last().GetIsUndoImmediately());

                if (undoList.Count != 0)
                {
                    // GD.Print("next undo action: ", undoList.Last().GetUndoDescription());
                }

                // GD.Print("remaining undo actions: ", undoList.Count);
            })
            .AddTo(disposables);
    }

    public virtual void ListenForAbilitySelected()
    {
        implementation.GetAbilityDialogController().OnEffectCompletedAsObservable()
            .Subscribe(__ => { RemoveLeaveEffectDialog(); })
            .AddTo(disposeOnBattleEnd);

        implementation.GetAbilitySelector()
            .OnAbilitySelectedAsObservable()
            .ObserveOn(SynchronizationContext.Current!) // Observe on the main thread
            .Subscribe(a =>
            {
                implementation.GetAbilitySelector().Close();
                implementation.SetCloseButtonVisibility(false);
                implementation.GetAbilityDialogController()
                    .DisplayAbilityDialog(a, undoList, battleCharacters, monsters,
                        implementation.OnBackButtonAsObservable());
                undoList.Add(new LeaveEffectDialog(a.GetCaster(), implementation));
            })
            .AddTo(disposeOnBattleEnd); // Don't forget to add to disposables
    }

    public void SetTheTeam(ICharacterData[] team)
    {
        for (int i = 0; i < team.Length; i++)
        {
            ICharacterData member = team[i];
            IBattleCharacterController characterController =
                implementation.CreateBattleCharacter();
            characterController.Init(member);
            ListenForBattleCharacterClicks(characterController);
            battleCharacters.Add(characterController);
        }
    }

    public void SetTheMonsters(List<IMonsterData> m)
    {
        this.monsters.Clear();
        for (int i = 0; i < m.Count; i++)
        {
            IMonsterData monster = m[i];
            IMonsterController monsterController =
                implementation.CreateMonster();
            monsterController.Init(monster);
            this.monsters.Add(monsterController);
        }
    }

    public virtual void ListenForBattleCharacterClicks(IBattleCharacterController c)
    {
        c.OnPressedAsObservable()
            .Where(_ => implementation.GetAbilitySelector().IsVisible() == false)
            .Where(_ => c.GetHasAction())
            .Where(_ => implementation.GetAbilityDialogController().GetReticuleCount() == 0)
            .Subscribe(_ =>
            {
                // Handle the click event for the battle character
                // For example, show the ability selector
                implementation.GetAbilitySelector().ShowAbilitySelector(c);
                implementation.SetCloseButtonVisibility(true);
            })
            .AddTo(disposables);
    }

    void ListenForCloseButtonClick()
    {
        implementation.OnCloseButtonClickedAsObservable()
            .Subscribe(_ =>
            {
                // Handle the close button click event
                implementation.SetCloseButtonVisibility(false);
                implementation.GetAbilitySelector().Close();
            })
            .AddTo(disposables);
    }

    public void RemoveLeaveEffectDialog()
    {
        undoList.RemoveAll(undo => undo is LeaveEffectDialog);
    }

    public void ListenForAllMonstersReducedToZero()
    {
        monsters.Select(m => m.ReducedToZeroAsObservable())
            .WhenAll()
            .Subscribe(_ =>
            {
                // destroy all monsters
                disposeOnRoundEnd.Dispose();
                stopManagerController.FinishBattle().Forget();
            }).AddTo(disposeOnBattleEnd);
    }

    public void DebugDamageAllMonsters()
    {
        monsters.ForEach(m=>m.TakeHealthDamage(1000));
    }
    


    private class LeaveEffectDialog(IBattleCharacterController c, IBattleManager imp) : IUndo
    {
        public void UndoEffect()
        {
            imp.GetAbilitySelector().ShowAbilitySelector(c);
            imp.SetCloseButtonVisibility(true);
            imp.GetAbilityDialogController().ClearDialog();
        }

        public bool GetIsUndoImmediately()
        {
            return true;
        }

        public string GetUndoDescription()
        {
            return "show ability selector and clear dialog";
        }
    }
}

public interface IBattleManagerController : IDisposable
{
    void Init();

    public IMonsterController[] GetMonsters();
}

public interface IBattleManager
{
    IAbilitySelectorController GetAbilitySelector();
    void                       SetCloseButtonVisibility(bool b);
    IBattleCharacterController CreateBattleCharacter();
    IObservable<Unit>          OnCloseButtonClickedAsObservable();
    ICharacterData[] GetTeam();
    List<IMonsterData> GetMonsters();
    IMonsterController CreateMonster();
    IAbilityDialogController GetAbilityDialogController();
    IObservable<Unit> OnBackButtonAsObservable();
    Task WaitForProcessFrame();
    void SetNextButtonVisible(bool b);
    IObservable<Unit> OnNextButtonClickedAsObservable();
    IDrawArrow MakeDrawArrow(Node2D start, Node2D end, Color color);
    IDisposable ShowClear();
    void SetPartyPosition(int stopIndex);
    void CreateStops(int numberOfStops);
    IBattleLootController DisplayLootOn(IMonsterController monster);
}
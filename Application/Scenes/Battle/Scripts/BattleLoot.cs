using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using BringMeTreasure.GodotAsync;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Battle;

public partial class BattleLoot : TextureButton, IBattleLoot
{
	private BattleLootController controller;
	public IBattleLootController Controller => controller ??= new BattleLootController(this);
	[Export] public Label amountLabel;

	public override void _Ready()
	{
		Controller.AddTo(this);
	}

	public void SetTexture(Texture2D getItemIconPath)
	{
		TextureNormal   = getItemIconPath;
		TexturePressed  = getItemIconPath;
		TextureDisabled = getItemIconPath;
	}

	public void SetAmount(int amount)
	{
		amountLabel.Text = amount.ToString();
	}

	public Vector2 GetCenterPosition()
	{
		return GlobalPosition + (Size / 2);
	}

	public IObservable<bool> OnPressedAsObservable()
	{
		return this.OnButtonPressedAsObservable().Select(_ => true);
	}

	public async Task Fade()
	{
		const float fadeOutDuration = 0.5f;
		CreateTween().TweenProperty(this, "modulate", Colors.Transparent, fadeOutDuration)
			.SetTrans(Tween.TransitionType.Quart)
			.SetEase(Tween.EaseType.In);
		await Task.Delay(TimeSpan.FromSeconds(fadeOutDuration));
	}
}

public class BattleLootController(IBattleLoot implementation) : IBattleLootController
{
	private Subject<Unit> pressedSubject = new();
	private CompositeDisposable disposables = new();

	public void Ready()
	{
		implementation.OnPressedAsObservable().Subscribe(_ => { AnimationTask().Forget(); }).AddTo(disposables);
	}

	private async Task AnimationTask()
	{
		await implementation.Fade();
		pressedSubject.OnNext(Unit.Default);
	}

	public void DisplayLoot(ISupplyItem lootToDisplay)
	{
		implementation.SetTexture(lootToDisplay.GetIcon());
		implementation.SetVisible(true);
	}

	public void SetAmount(int amount)
	{
		implementation.SetAmount(amount);
	}

	public void QueueFree()
	{
		implementation.QueueFree();
	}

	public void SetVisible(bool b)
	{
		implementation.SetVisible(b);
	}

	public IObservable<bool> OnPressedAsObservable()
	{
		return pressedSubject.Select(_ => true).AsObservable();
	}

	public Vector2 GetCenterPosition()
	{
		return implementation.GetCenterPosition();
	}

	public Control GetNode()
	{
		return implementation as BattleLoot;
	}

	public void SendPressedEvent()
	{
		pressedSubject.OnNext(Unit.Default);
	}

	public void Dispose()
	{
		disposables.Dispose();
	}
}

public interface IBattleLootController : IDisposable
{
	void DisplayLoot(ISupplyItem lootToDisplay);
	void SetAmount(int amount);
	public void QueueFree();
	void SetVisible(bool b);
	public IObservable<bool> OnPressedAsObservable();
	void Ready();
}

public interface IBattleLoot
{
	void SetTexture(Texture2D getItemIconPath);
	void QueueFree();
	void SetAmount(int amount);
	void SetVisible(bool b);
	Vector2 GetCenterPosition();
	IObservable<bool> OnPressedAsObservable();
	Task Fade();
}

using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Battle;

public partial class AbilitySelector : Control, IAbilitySelector
{
    [Export] AbilityButton originalAbilityButton;
    [Export] Control abilityDescriptionPanel;
    [Export] Label abilityNameLabel;
    [Export] RichTextLabel abilityDescriptionLabel;
    private AbilitySelectorController controller;
    public IAbilitySelectorController Controller => controller ??= new AbilitySelectorController(this);

    public void OpenAbilitySelector()
    {
        Visible = true;
    }

    public override void _Ready()
    {
        Controller.AddTo(this);
        Hide();
        abilityDescriptionPanel.Hide();
    }

    public IAbilityButtonController CreateAbilityButton(ICombatAbility combatAbility)
    {
        AbilityButton abilityButton = (AbilityButton) originalAbilityButton.Duplicate();
        abilityButton.SetAbilityName(combatAbility.GetName());
        abilityButton.SetUses(combatAbility.GetUses());
        originalAbilityButton.GetParent().AddChild(abilityButton);
        return abilityButton.Controller;
    }

    public void SetDescriptionName(string abilityName)
    {
        abilityNameLabel.Text = abilityName;
    }

    public void SetAbilityDescriptionLabel(string abilityDescription)
    {
        abilityDescriptionLabel.BbcodeEnabled = true;
        abilityDescriptionLabel.ParseBbcode(abilityDescription);
    }

    public void SetDescriptionVisible(bool b)
    {
        abilityDescriptionPanel.Visible = b;
    }

}

public class AbilitySelectorController(IAbilitySelector implementation) : IAbilitySelectorController
{
    private List<IAbilityButtonController> abilityButtons = new();
    private readonly Subject<ISelectedCombatAbility> abilitySelectedSubject = new();

    public void ShowAbilitySelector(IBattleCharacterController character)
    {
        abilityButtons.ForEach(button => button.Destroy());
        abilityButtons.Clear();

        character.GetCharacterData().GetCombatAbilities().ForEach(ability =>
        {
            ISelectedCombatAbility selectedCombatAbility = new SelectedCombatAbility(ability, character);
            IAbilityButtonController abilityButtonController = implementation.CreateAbilityButton(selectedCombatAbility);
            abilityButtonController.ShowAbilityButton(selectedCombatAbility);
            abilityButtons.Add(abilityButtonController);
            IDisposable hoverFeature = MakeHoverFeature(abilityButtonController, selectedCombatAbility);
            abilityButtonController.AddDisposable(hoverFeature);
            abilityButtonController.OnAbilitySelectedAsObservable()
                .Subscribe(_ =>
                {
                    abilitySelectedSubject.OnNext(selectedCombatAbility);
                });
        });
        implementation.OpenAbilitySelector();
    }

    private IDisposable MakeHoverFeature(IAbilityButtonController abilityButtonController, ICombatAbility combatAbility)
    {
        IDisposable hoverFeature = abilityButtonController.OnMouseEntered().Subscribe(_ =>
        {
            implementation.SetDescriptionName(combatAbility.GetName());
            implementation.SetAbilityDescriptionLabel(combatAbility.GetDescription());
            implementation.SetDescriptionVisible(true);
            IDisposable close = abilityButtonController.OnMouseExitedAsObservable()
                .Take(1)
                .Subscribe(__ =>
                {
                    implementation.SetDescriptionVisible(false);
                });
            abilityButtonController.AddDisposable(close);
        });
        return hoverFeature;
    }

    public void Close()
    {
        implementation.SetVisible(false);
    }

    public bool IsVisible()
    {
        return implementation.IsVisible();
    }

    public IObservable<ISelectedCombatAbility> OnAbilitySelectedAsObservable()
    {
        return abilitySelectedSubject.AsObservable().Throttle(TimeSpan.FromMilliseconds(10));
    }

    public void Dispose()
    {
        // TODO release managed resources here
    }
}

public interface IAbilitySelectorController : IDisposable
{
    void ShowAbilitySelector(IBattleCharacterController character);
    void Close();
    bool IsVisible();

    IObservable<ISelectedCombatAbility> OnAbilitySelectedAsObservable();
}

public interface IAbilitySelector
{
    public void OpenAbilitySelector();
    IAbilityButtonController CreateAbilityButton(ICombatAbility combatAbility);
    void SetVisible(bool b);
    void SetDescriptionName(string abilityName);
    void SetAbilityDescriptionLabel(string abilityDescription);
    void SetDescriptionVisible(bool b);
    bool IsVisible();
}
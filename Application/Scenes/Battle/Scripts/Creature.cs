using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using BringMeTreasure.Shaders;
using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Battle;

[GlobalClass]
public partial class Creature : HBoxContainer, ICreature
{
    private CreatureController controller;
    public ICreatureController Controller => controller ??= new CreatureController(this);
    [Export] Texture2D characterTexture;
    [Export] bool flipHorizontally;
    [Export] protected TextureButton textureButton;
    [Export] AnimationPlayer animationPlayer;
    [Export] CharacterPointDisplay characterPointDisplay;
    [Export] Node2D centerPositionNode;

    public Texture2D GetTexture()
    {
        return characterTexture;
    }

    public void SetTexture(Texture2D texture)
    {
        if (textureButton == null)
        {
            return;
        }

        textureButton.TextureNormal = texture;
        textureButton.TexturePressed = texture;
        textureButton.TextureHover = texture;
        textureButton.FlipH = flipHorizontally;
    }

    public IObservable<Unit> OnCharacterClickedAsObservable()
    {
        return textureButton.OnButtonPressedAsObservable();
    }

    public void PlayIdleAnimation()
    {
        animationPlayer.Play("idle");
    }

    public void Destroy()
    {
        QueueFree();
    }

    public ICharacterPointDisplayController GetCharacterPointDisplayController()
    {
        return characterPointDisplay.Controller;
    }

    public IGlowShader GetNewGlowShader()
    {
        ShaderMaterial uniqueMaterial = (ShaderMaterial) textureButton.Material.Duplicate();
        textureButton.Material = uniqueMaterial; // Assign the duplicated material back to the button
        return new GlowShaderController(uniqueMaterial);
    }

    public override void _Ready()
    {
        base._Ready();
        Controller.AddTo(this);
        _ = new CenterOnControl(centerPositionNode, textureButton);
    }

    public void SetPivotToBottom()
    {
        Vector2 size = textureButton.GetSize();
        textureButton.SetPivotOffset(new Vector2(size.X, size.Y));
    }

    public IObservable<Unit> OnPressedAsObservable()
    {
        return textureButton.OnButtonPressedAsObservable();
    }

    public IObservable<Unit> OnHoveredAsObservable()
    {
        return textureButton.OnMouseEnterAsObservable();
    }

    public IObservable<Unit> OnUnhoveredAsObservable()
    {
        return textureButton.OnMouseExitAsObservable();
    }

    public void SetInteractive(bool b)
    {
        textureButton.Disabled = !b;
    }

    public bool GetIsDisabled()
    {
        return textureButton.IsDisabled();
    }

    public void StopAnimation()
    {
        if (animationPlayer.IsPlaying())
        {
            animationPlayer.Stop();
        }
    }

    public IDisposable PlayTakeDamageTween()
    {
        Vector2 originalPosition = textureButton.Position;
        const float damageShakeAmount = 10;
        const float moveTime = 0.05f;
        Tween damageShuffle = CreateTween();
        // i want the position to move side to side a bit
        damageShuffle.TweenProperty(textureButton, "position",
            textureButton.Position + new Vector2(damageShakeAmount, 0), moveTime);
        damageShuffle.TweenProperty(textureButton, "position", originalPosition, moveTime);
        damageShuffle.TweenProperty(textureButton, "position",
            textureButton.Position - new Vector2(damageShakeAmount, 0), moveTime);
        damageShuffle.TweenProperty(textureButton, "position", originalPosition, moveTime);
        damageShuffle.SetLoops(2);
        damageShuffle.Play();
        return Disposable.Create(() =>
        {
            damageShuffle.Kill();
            textureButton.Position = originalPosition;
        });
    }

    public Node2D GetCenterPosition()
    {
        
        return centerPositionNode;
    }

    public Tween MakeTween()
    {
        return CreateTween();
    }

    public TextureButton GetTextureButton()
    {
        return textureButton;
    }
}

public class CreatureController(ICreature implementation) : ICreatureController
{
    public const int MaxPositionPoints = 25;
    private readonly CompositeDisposable disposables = new();
    protected int healthPoints;
    protected int armorPoints;
    protected int positionPoints;
    protected IGlowShader glowShader;
    private Subject<Unit> reducedToZeroSubject = new();
    private IDisposable damageTweenDisposable = Disposable.Empty;

    public virtual void Init()
    {
        implementation.SetPivotToBottom();
        glowShader = implementation.GetNewGlowShader();
    }

    public void UpdateDisplay()
    {
        ICharacterPointDisplayController characterPointDisplayController =
            implementation.GetCharacterPointDisplayController();
        characterPointDisplayController.SetHealthPoints(healthPoints);
        characterPointDisplayController.SetArmorPoints(armorPoints);
        characterPointDisplayController.SetPositionPoints(positionPoints);
    }

    public void Dispose()
    {
        disposables.Dispose();
    }

    public int TakeHealthDamage(int damageAmount)
    {
        if (positionPoints == 0)
        {
            damageAmount *= 2; // double damage if position points are zero
        }

        if (positionPoints == MaxPositionPoints)
        {
            damageAmount = 0;
        }

        int actualDamage = 0;
        if (damageAmount >= healthPoints)
        {
            actualDamage = healthPoints;
        }
        else
        {
            actualDamage = damageAmount;
        }

        healthPoints -= actualDamage;

        if (healthPoints == 0)
        {
            ReducedToZero();
        }
        else
        {
            // only play this tween if the creature is not dead
            PlayTakeDamageTween();
        }

        UpdateDisplay();
        return actualDamage;
    }

    public int TakeArmorDamage(int damageAmount)
    {
        if (positionPoints == 0)
        {
            damageAmount *= 2; // double damage if position points are zero
        }

        if (positionPoints == MaxPositionPoints)
        {
            damageAmount = 0;
        }
        
        int actualDamage = 0;
        if (damageAmount >= armorPoints)
        {
            actualDamage = armorPoints;
        }
        else
        {
            actualDamage = damageAmount;
        }

        armorPoints -= actualDamage;
        UpdateDisplay();
        return actualDamage;
    }

    public int TakePositionDamage(int damageAmount)
    {
        PlayTakeDamageTween();
        int actualDamage = 0;
        if (damageAmount >= positionPoints)
        {
            actualDamage = positionPoints;
        }
        else
        {
            actualDamage = damageAmount;
        }

        positionPoints -= actualDamage;
        UpdateDisplay();
        return actualDamage;
    }

    public void UndoArmorDamage(int armorDamageDone)
    {
        damageTweenDisposable.Dispose();
        armorPoints += armorDamageDone;
        UpdateDisplay();
    }

    public void UndoHealthDamage(int healthDamageDone)
    {
        damageTweenDisposable.Dispose();
        bool isDead = healthPoints == 0;
        healthPoints += healthDamageDone;
        UpdateDisplay();

        if (isDead)
        {
            UndoReducedToZero();
        }
    }

    public void UndoPositionDamage(int positionDamageDone)
    {
        damageTweenDisposable.Dispose();
        positionPoints += positionDamageDone;
        if (positionPoints > MaxPositionPoints)
        {
            positionPoints = MaxPositionPoints; // Ensure position points do not exceed max
        }

        UpdateDisplay();
    }

    public void SetOutline(Color color)
    {
        if (healthPoints == 0)
        {
            return;
        }

        glowShader.SetColor(color);
        glowShader.SetActiveAndThickness(1);
    }

    public void HideOutline()
    {
        glowShader.SetActive(false);
    }

    public IObservable<Unit> OnPressedAsObservable()
    {
        return implementation.OnPressedAsObservable();
    }

    public void GainArmor(int amount)
    {
        armorPoints += amount;
        UpdateDisplay();
    }

    protected virtual void ReducedToZero()
    {
        reducedToZeroSubject.OnNext(Unit.Default);
    }

    protected virtual void UndoReducedToZero()
    {
        // This can be overridden to handle undo logic when the creature is reduced to zero health
    }

    public IObservable<Unit> OnHoveredAsObservable()
    {
        return implementation.OnHoveredAsObservable().Where(_ => implementation.GetIsDisabled() == false);
    }

    public IObservable<Unit> OnUnhoveredAsObservable()
    {
        return implementation.OnUnhoveredAsObservable().Merge(reducedToZeroSubject);
    }

    public int GetHealthPoints()
    {
        return healthPoints;
    }

    public Node2D GetCenterPosition()
    {
        return implementation.GetCenterPosition();
    }

    private void PlayTakeDamageTween()
    {
        damageTweenDisposable?.Dispose();
        damageTweenDisposable = implementation.PlayTakeDamageTween();
        disposables.Add(damageTweenDisposable);
    }
}

public interface ICreatureController : IDisposable
{
    /// <summary>
    /// takes damage from some source
    /// returns the actual amount of damage taken as it may be reduced or increased by position 
    /// </summary>
    /// <param name="damageAmount"></param>
    /// <returns>the amount of damage taken</returns>
    int TakeHealthDamage(int damageAmount);

    int TakeArmorDamage(int damageAmount);
    int TakePositionDamage(int damageAmount);
    void UndoArmorDamage(int armorDamageDone);
    void UndoHealthDamage(int healthDamageDone);
    void UndoPositionDamage(int positionDamageDone);
    void SetOutline(Color color);
    void HideOutline();
    IObservable<Unit> OnPressedAsObservable();
    void GainArmor(int amount);
    IObservable<Unit> OnHoveredAsObservable();
    IObservable<Unit> OnUnhoveredAsObservable();
    int GetHealthPoints();
    Node2D GetCenterPosition();
}

public interface ICreature
{
    void SetTexture(Texture2D texture);
    public IObservable<Unit> OnCharacterClickedAsObservable();
    public void PlayIdleAnimation();
    void Destroy();
    public ICharacterPointDisplayController GetCharacterPointDisplayController();
    public IGlowShader GetNewGlowShader();
    public void SetPivotToBottom();
    IObservable<Unit> OnPressedAsObservable();
    IObservable<Unit> OnHoveredAsObservable();
    IObservable<Unit> OnUnhoveredAsObservable();
    void SetInteractive(bool b);
    bool GetIsDisabled();
    void StopAnimation();
    IDisposable PlayTakeDamageTween();
    Node2D GetCenterPosition();
    Tween MakeTween();
    TextureButton GetTextureButton();
    
    
}
using Godot;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Scripts.Characters;

namespace BringMeTreasure.Battle;

[GlobalClass]
public partial class BattleCharacter : Creature, IBattleCharacter
{
	private BattleCharacterController controller;
	public new BattleCharacterController Controller => controller ??= new BattleCharacterController(this);
	public void SetTextureModulate(Color c)
	{
		textureButton.SetModulate(c);
	}
}

public class BattleCharacterController(IBattleCharacter implementation)
	: CreatureController(implementation), IBattleCharacterController
{
	private ICharacterData characterData;
	private bool hasAction;
	private Subject<bool> actionChangedSubject = new();

	public void Init(ICharacterData characterData)
	{
		base.Init();
		implementation.PlayIdleAnimation();
		implementation.SetTexture(characterData.GetTexture());
		ICharacterPointDisplayController characterPointDisplayController =
			implementation.GetCharacterPointDisplayController();
		healthPoints = characterData.GetHealthPoints();
		armorPoints = characterData.GetArmorPoints();
		positionPoints = characterData.GetPositioningPoints();
		UpdateDisplay();
		this.characterData = characterData;
	}

	public ICharacterData GetCharacterData()
	{
		return characterData;
	}

	public void SetHasActionAndUi(bool b)
	{
		hasAction = b;
		implementation.SetTextureModulate(b ? Colors.White : Colors.Gray);
		actionChangedSubject.OnNext(b);
	}

	public bool GetHasAction()
	{
		return hasAction;
	}

	public IObservable<bool> OnActionChangedAsObservable()
	{
		return actionChangedSubject.AsObservable();
	}
}

public interface IBattleCharacterController : ICreatureController
{
	void Init(ICharacterData characterData);
	ICharacterData GetCharacterData();

	/// <summary>
	/// also updates UI
	/// </summary>
	/// <param name="b"></param>
	public void SetHasActionAndUi(bool b);

	bool GetHasAction();
	IObservable<bool> OnActionChangedAsObservable();
}

public interface IBattleCharacter : ICreature
{
	void SetTextureModulate(Color c);
}

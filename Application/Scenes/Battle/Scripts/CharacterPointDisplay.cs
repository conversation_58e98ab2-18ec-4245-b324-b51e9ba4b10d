using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.Battle;

public partial class CharacterPointDisplay : Control, ICharacterPointDisplay
{
	[Export] private Label healthPLabel;
	[Export] private Label positionPLabel;
	[Export] private Label armorPLabel;

	private CharacterPointDisplayController controller;
	public ICharacterPointDisplayController Controller => controller ??= new CharacterPointDisplayController(this);

	public override void _Ready()
	{
		base._Ready();
		Controller.AddTo(this);
	}

	public void SetHealthPoints(int healthPoints)
	{
		healthPLabel.Text = healthPoints.ToString();
	}

	public void SetPositionPoints(int positionPoints)
	{
		positionPLabel.Text = positionPoints.ToString();
	}

	public void SetArmorPoints(int armorPoints)
	{
		armorPLabel.Text = armorPoints.ToString();
	}
}

public class CharacterPointDisplayController(ICharacterPointDisplay implementation) : ICharacterPointDisplayController
{
	public void SetHealthPoints(int healthPoints)
	{
		implementation.SetHealthPoints(healthPoints);
		implementation.SetVisible(healthPoints > 0);
	}

	public void SetPositionPoints(int positionPoints)
	{
		implementation.SetPositionPoints(positionPoints);
	}

	public void SetArmorPoints(int armorPoints)
	{
		implementation.SetArmorPoints(armorPoints);
	}

	public void Dispose()
	{
	}
}

public interface ICharacterPointDisplayController : IDisposable
{
	void SetHealthPoints(int healthPoints);
	void SetPositionPoints(int positionPoints);
	void SetArmorPoints(int armorPoints);
}

public interface ICharacterPointDisplay
{
	void SetHealthPoints(int healthPoints);
	void SetPositionPoints(int positionPoints);
	void SetArmorPoints(int armorPoints);
	void SetVisible(bool b);
}

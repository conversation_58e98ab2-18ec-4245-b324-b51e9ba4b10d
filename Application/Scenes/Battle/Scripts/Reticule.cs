using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Application;
using BringMeTreasure.Behaviors;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using BringMeTreasure.Shaders;
using Godot;

namespace BringMeTreasure.Battle;

[GlobalClass]
public partial class Reticule : Control, IReticule
{
	private ReticuleController controller;
	public IReticuleController Controller => controller ??= new ReticuleController(this);
	[Export] private Color modulateColor = Colors.White;
	[Export] private Texture2D attackEnemyTexture;
	[Export] private TextureButton textureButton;

	public override void _Ready()
	{
		base._Ready();
		Controller.Ready();
		textureButton.Modulate = modulateColor;
		controller.AddTo(this);
	}

	public IDisposable MakeTintBehavior()
	{
		return new PropagateTint(textureButton, modulateColor);
	}

	public void SetTexture(ReticuleType.ReticuleType reticuleType)
	{
		Texture2D selectedTexture;
		switch (reticuleType)
		{
			case ReticuleType.ReticuleType.AttackEnemy:
				selectedTexture = attackEnemyTexture;

				break;
			default:
				throw new ArgumentOutOfRangeException(nameof(reticuleType), reticuleType, null);
		}

		textureButton.TextureNormal = selectedTexture;
		textureButton.TexturePressed = selectedTexture;
		textureButton.TextureDisabled = selectedTexture;
	}

	public void SetTextureVisible(bool b)
	{
		textureButton.SetVisible(b);
	}

	public IObservable<Unit> OnPressedAsObservable()
	{
		return textureButton.OnButtonPressedAsObservable();
	}

	public bool IsTextureVisible()
	{
		return textureButton.IsVisible();
	}

	public void SetPressedNoSignal(bool pressed)
	{
		textureButton.SetPressedNoSignal(pressed);
	}

	public void SetPressed(bool pressed)
	{
		textureButton.SetPressed(pressed);
	}

	public bool IsPressed()
	{
		return textureButton.IsPressed();
	}

	public void SetToggleMode(bool enabled)
	{
		textureButton.SetToggleMode(enabled);
	}

	public IObservable<bool> OnToggleChangedAsObservable()
	{
		return textureButton.OnToggledAsObservable();
	}

	public IGlowShader GetNewGlowShader()
	{
		ShaderMaterial uniqueMaterial = (ShaderMaterial) textureButton.Material.Duplicate();
		textureButton.Material = uniqueMaterial; // Assign the duplicated material back to the button
		return new GlowShaderController(uniqueMaterial);
	}

	public void SetPlaceHolderVisible(bool b)
	{
		SetVisible(b);
	}

	public bool GetIsPressed()
	{
		return textureButton.IsPressed();
	}

	public void SetDisabled(bool b)
	{
		textureButton.SetDisabled(b);
	}
}

public class ReticuleController(IReticule implementation) : IReticuleController
{
	private readonly CompositeDisposable disposable = new();
	private CompositeDisposable disposeOnUnToggle = new();
	private Subject<bool> onCompleteSubject = new();
	private Subject<Unit> onToggledOnSubject = new();
	private IGlowShader glowShader;
	List<IBattleCharacterController> team;
	List<IMonsterController> monsters;
	private ICombatAbilityEffect effect;

	public void Ready()
	{
		implementation.MakeTintBehavior()
			.AddTo(disposable);
		glowShader = implementation.GetNewGlowShader();
		disposeOnUnToggle.AddTo(disposable);
	}


	public void SetEffect(ICombatAbilityEffect effect, List<IUndo> undoList, List<IBattleCharacterController> team,
		List<IMonsterController> monsters)
	{
		this.effect = effect;
		this.team = team;
		this.monsters = monsters;
		// set image
		implementation.SetTexture(effect.GetReticuleType());
		// turn reticule on
		implementation.SetTextureVisible(true);
		// listen for press
		ListenForPress(effect, undoList, team, monsters);
	}

	public void Destroy()
	{
		Dispose();
		implementation.QueueFree();
	}

	public IObservable<bool> OnCompleteAsObservable()
	{
		return onCompleteSubject.AsObservable();
	}

	public bool IsTextureVisible()
	{
		return implementation.IsTextureVisible();
	}

	public void Select()
	{

		if (implementation.GetIsPressed())
		{
			return;
		}
		
		implementation.SetPressed(true);
	}


	public void ListenForPress(ICombatAbilityEffect effect, List<IUndo> undoList, List<IBattleCharacterController> team,
		List<IMonsterController> monsters)
	{
		implementation.SetPlaceHolderVisible(true);
		onToggledOnSubject.AsObservable()
			.Subscribe(_ =>
			{
				if (effect.GetTargetsSelf())
				{
					throw new ArgumentException("Cannot target self with reticule");
				}
				glowShader.SetColor(Colors.Blue);
				glowShader.SetActiveAndThickness(5);
				// draw target marks on characters
				//draw on monsters
				if (effect.GetTargetsEnemy())
				{
					monsters.ForEach(m => { m.SetOutline(effect.GetOutlineColor()); });
					monsters.Select(m => m.OnPressedAsObservable().Select(_ => m))
						.Merge()
						.Take(1)
						.Subscribe(m =>
						{
							DoEffect(effect, undoList, m);
							monsters.ForEach(c => c.HideOutline());
							implementation.SetTextureVisible(false);
							// OnUntoggled();
							onCompleteSubject.OnNext(true);
						}).AddTo(disposeOnUnToggle);
				}

				if (effect.GetTargetsTeam())
				{
					//draw on team
					team.ForEach(c => { c.SetOutline(effect.GetOutlineColor()); });
					team.Select(t => t.OnPressedAsObservable().Select(_ => t))
						.Merge()
						.Take(1)
						.Subscribe(t =>
						{
							DoEffect(effect, undoList, t);
							team.ForEach(c => c.HideOutline());
							implementation.SetTextureVisible(false);
							// OnUntoggled();
							onCompleteSubject.OnNext(true);
						}).AddTo(disposeOnUnToggle);
				}


				// listen for character presses 
			}).AddTo(disposable);
	}

	private void DoEffect(ICombatAbilityEffect effect, List<IUndo> undoList, ICreatureController c)
	{
		IDisposable thisCannotFireAfterTheRetqIsDestroyed = Disposable.Create(() =>
		{
			implementation.SetTextureVisible(true);
		});
		thisCannotFireAfterTheRetqIsDestroyed.AddTo(disposable);


		undoList.Add(new Undo(() =>
		{
			thisCannotFireAfterTheRetqIsDestroyed.Dispose();
			onCompleteSubject.OnNext(false);
		}, "reticule => visible, uncomplete", true));
		undoList.Add(effect.ApplyTo(c));
	}


	public void Dispose()
	{
		disposable.Dispose();
	}

	public void SetPressedNoSignal(bool pressed)
	{
		implementation.SetPressed(pressed);
	}

	public bool IsPressed()
	{
		return implementation.IsPressed();
	}

	void IToggleButton.SetToggleMode(bool enabled)
	{
		implementation.SetToggleMode(enabled);
	}

	public void OnUntoggled()
	{
		glowShader.SetActive(false);
		disposeOnUnToggle.Dispose();
		disposeOnUnToggle = new CompositeDisposable();
		disposeOnUnToggle.AddTo(disposable);
		team.ForEach(c => c.HideOutline());
		monsters.ForEach(c => c.HideOutline());
	}

	public void OnToggled()
	{
		onToggledOnSubject.OnNext(Unit.Default);
	}

	IObservable<bool> IToggleButton.OnToggledAsObservable()
	{
		return implementation.OnToggleChangedAsObservable();
	}

	public void SetDisabled(bool b)
	{
		implementation.SetDisabled(b);
	}
}

public interface IReticuleController : IDisposable, IToggleButton
{
	void Ready();

	public void SetEffect(ICombatAbilityEffect effect, List<IUndo> undoList, List<IBattleCharacterController> team,
		List<IMonsterController> monsters);

	void Destroy();
	IObservable<bool> OnCompleteAsObservable();
	bool IsTextureVisible();
	void Select();
}

public interface IReticule
{
	public IDisposable MakeTintBehavior();
	void SetTexture(ReticuleType.ReticuleType reticuleType);
	void SetTextureVisible(bool b);
	IObservable<Unit> OnPressedAsObservable();
	void QueueFree();
	bool IsTextureVisible();
	void SetPressedNoSignal(bool pressed);
	void SetPressed(bool pressed);
	bool IsPressed();
	void SetToggleMode(bool enabled);
	IObservable<bool> OnToggleChangedAsObservable();
	IGlowShader GetNewGlowShader();
	void SetPlaceHolderVisible(bool b);
	bool GetIsPressed();
	void SetDisabled(bool b);
}

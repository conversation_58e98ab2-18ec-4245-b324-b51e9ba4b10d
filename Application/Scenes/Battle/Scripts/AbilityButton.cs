using System.Reactive;
using System.Reactive.Disposables;
using BringMeTreasure.Behaviors;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Battle;

public partial class AbilityButton : Button, IAbilityButton
{
    [Export] private Label abilityNameLabel;
    [Export] private Label usesLabel;

    private AbilityButtonController controller;
    public IAbilityButtonController Controller => controller ??= new AbilityButtonController(this);

    public override void _Ready()
    {
        Controller.AddTo(this);
        new PropagateTint(this).AddTo(this);
    }

    public void SetAbilityName(string abilityName)
    {
        abilityNameLabel.Text = abilityName;
    }

    public void SetUses(int abilityUses)
    {
        usesLabel.Text = abilityUses.ToString();
    }

    public IObservable<Unit> OnMouseEnteredAsObservable()
    {
        return this.OnMouseEnterAsObservable();
    }

    public IObservable<Unit> OnMouseExitedAsObservable()
    {
        return this.OnMouseExitAsObservable();
    }

    public IObservable<Unit> OnPressedAsObservable()
    {
        return this.OnButtonPressedAsObservable();
    }
}

public class AbilityButtonController(IAbilityButton implementation) : IAbilityButtonController
{
    private readonly CompositeDisposable disposable = new();
    public void Dispose()
    {
        disposable.Dispose();
    }

    public void ShowAbilityButton(ICombatAbility combatAbility)
    {
        implementation.SetAbilityName(combatAbility.GetName());
        implementation.SetUses(combatAbility.GetUses());
        implementation.Show();
    }

    public void Destroy()
    {
        implementation.QueueFree();
    }

    public IObservable<Unit> OnMouseEntered()
    {
        return implementation.OnMouseEnteredAsObservable();
    }

    public IObservable<Unit> OnMouseExitedAsObservable()
    {
        return implementation.OnMouseExitedAsObservable();
    }

    public void AddDisposable(IDisposable d)
    {
        disposable.Add(d);
    }

    public IObservable<Unit> OnAbilitySelectedAsObservable()
    {
        return implementation.OnPressedAsObservable();
    }
}

public interface IAbilityButtonController : IDisposable
{
    void ShowAbilityButton(ICombatAbility combatAbility);
    void Destroy();
    IObservable<Unit> OnMouseEntered();
    IObservable<Unit> OnMouseExitedAsObservable();
    void AddDisposable(IDisposable d);
    IObservable<Unit> OnAbilitySelectedAsObservable();
}

public interface IAbilityButton
{
    void SetAbilityName(string abilityName);
    void SetUses(int abilityUses);
    void Show();
    void QueueFree();
    IObservable<Unit> OnMouseEnteredAsObservable();
    IObservable<Unit> OnMouseExitedAsObservable();
    IObservable<Unit> OnPressedAsObservable();
}
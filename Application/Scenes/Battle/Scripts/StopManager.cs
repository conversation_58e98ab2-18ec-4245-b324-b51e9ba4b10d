using BringMeTreasure.Extensions;
using BringMeTreasure.GodotAsync;
using BringMeTreasure.Models;
using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Battle;

public class StopManager : IStopManager
{
    public ISave GetSave()
    {
        return GameStateManager.Instance;
    }

    public IRoute LoadRoute(Routes currentRoute)
    {
        return Route.LoadRoute(currentRoute);
    }

    public void AutoSave()
    {
        GameStateManager.Instance.AutoSave();
    }
}

public interface IStopManager
{
    ISave GetSave();
    IRoute LoadRoute(Routes currentRoute);
    void AutoSave();
}

public class StopManagerController(
    IBattleManager bm,
    IStopManager implementation,
    BattleManagerController battleManagerController)
{
    IStop[] stopsWeWillBeUsing;
    int stopIndex = -1;
    private RandomNumberGenerator rng;

    private IMonsterData[] monsters;

    public void Start()
    {
        ISave  save         = implementation.GetSave();
        Routes currentRoute = save.GetKey<Routes>(nameof(ISave.Keys.CurrentRoute));
        GD.Print($"Current route: {currentRoute}");
        IRoute route = implementation.LoadRoute(currentRoute);

        rng = MakeRng(save);

        Vector2I stopRange     = route.GetStopRange();
        int      numberOfStops = rng.RandiRange(stopRange.X, stopRange.Y + 1);
        stopsWeWillBeUsing = route.GetStopsAlongTheRoute().Shuffle(new RNGWrapper(rng)).Take(numberOfStops).ToArray();

        if (save.HasKey(nameof(ISave.Keys.StopIndex)))
        {
            stopIndex = save.GetKey<int>(nameof(ISave.Keys.StopIndex));
        }


        bm.CreateStops(numberOfStops);
        bm.SetPartyPosition(stopIndex);

        if (save.HasKey(nameof(ISave.Keys.RngState)))
        {
            rng.State = save.GetKey<ulong>(nameof(ISave.Keys.RngState));
        }

        GoToNextStop().Forget();
    }

    public async Task GoToNextStop()
    {
        await bm.WaitForProcessFrame();
        await Task.Delay(1000); // Simulate some delay for going to the next stop

        ISave save = implementation.GetSave();
        save.SetKey(nameof(ISave.Keys.StopIndex), stopIndex);
        save.SetKey(nameof(ISave.Keys.RngState),  rng.State);
        implementation.AutoSave();
        stopIndex += 1;
        if (stopIndex >= stopsWeWillBeUsing.Length)
        {
            // done
            GoToDestination().Forget();
            return;
        }


        //todo animation?
        bm.SetPartyPosition(stopIndex);
        battleManagerController.SetTheMonsters(SetUpMonsters(stopsWeWillBeUsing[stopIndex]).ToList());
        battleManagerController.ReadyNewBattle().Forget();
    }

    public async Task FinishBattle()
    {
        ISupply                     supply          = implementation.GetSave().GetSupply();
        List<IBattleLootController> lootControllers = new();
        foreach (IMonsterController monster in battleManagerController.GetMonsters())
        {
            IMonsterData     md          = monster.GetMonsterData();
            SupplyItemType[] rolledLoot  = md.GetLootTable().Roll(new RNGWrapper(rng), 1);
            ISupplyItem[]    supplyItems = rolledLoot.Select(SupplyItemResource.LoadItem).ToArray();

            for (int i = 0; i < rolledLoot.Distinct().Count(); i++)
            {
                IBattleLootController itemOnTheScreen = bm.DisplayLootOn(monster);
                itemOnTheScreen.Ready();
                itemOnTheScreen.DisplayLoot(supplyItems[i]);
                itemOnTheScreen.SetAmount(rolledLoot.Count(item => item == rolledLoot[i]));
                itemOnTheScreen.SetVisible(true);
                lootControllers.Add(itemOnTheScreen);
            }

            foreach (SupplyItemType item in rolledLoot)
            {
                supply.AddSupplyItem(item, 1);
            }
        }


        await Task.WhenAll(battleManagerController.GetMonsters().Select(m => m.FadeOut()));
        battleManagerController.GetMonsters().ForEach(m => m.QueueFree());

        lootControllers.Select(l => l.OnPressedAsObservable())
            .WhenAll()
            .Subscribe(_ =>
            {
                lootControllers.ForEach(l => l.QueueFree());
                GoToNextStop().Forget();
            });
    }


    private async Task GoToDestination()
    {
        ISceneManager sceneManager = SceneManager.Instance;
        ISave         save         = implementation.GetSave();
        save.SetKey(nameof(ISave.Keys.Arrived), true);
        save.RemoveKey(nameof(ISave.Keys.StopIndex));
        save.RemoveKey(nameof(ISave.Keys.RngSeed));
        save.RemoveKey(nameof(ISave.Keys.RngState));
        implementation.AutoSave();
        sceneManager.ChangeScene(ISceneManager.SceneType.Travel);
    }

    public IMonsterData[] SetUpMonsters(IStop currentStop)
    {
        monsters = currentStop.GetPossibleMonsters().Shuffle(new RNGWrapper(rng)).Take(4).ToArray();
        return monsters.ToArray();
    }

    private RandomNumberGenerator MakeRng(ISave save)
    {
        ulong seed;
        if (save.HasKey(nameof(ISave.Keys.RngSeed)))
        {
            seed = save.GetKey<ulong>(nameof(ISave.Keys.RngSeed));
        }
        else
        {
            seed = GD.Randi();
            save.SetKey(nameof(ISave.Keys.RngSeed), seed);
            implementation.AutoSave();
        }

        RandomNumberGenerator r = new();
        r.Seed = seed;
        return r;
    }
}
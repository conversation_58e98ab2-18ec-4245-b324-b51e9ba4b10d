using BringMeTreasure.Models;

namespace BringMeTreasure.Battle;



/// <summary>
/// Creating the action does the action.
/// also creates the undo action.
/// </summary>
/// <param name="character"></param>
/// <param name="target"></param>
/// <param name="combatAbility"></param>
public class ActionTakenOnCharacter(IBattleCharacterController character, IMonster target, ICombatAbility combatAbility):IActionTaken
{
    public void UndoAction()
    {
        throw new NotImplementedException();
    }
}

public class ActionTakenOnMonster(IBattleCharacterController character, IBattleCharacter target, ICombatAbility combatAbility):IActionTaken
{
    public void UndoAction()
    {
        throw new NotImplementedException();
    }
}

public interface IActionTaken
{
    public void UndoAction();
}
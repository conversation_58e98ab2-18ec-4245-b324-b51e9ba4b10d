using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Application;
using BringMeTreasure.Behaviors;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;


namespace BringMeTreasure.Battle;

public partial class AbilityDialog : Control, IAbilityDialog
{
    private AbilityDialogController controller;
    public IAbilityDialogController Controller => controller ??= new AbilityDialogController(this);

    [Export] private Reticule defaultReticule;

    public IReticuleController CreateBlankReticule()
    {
        Reticule reticule = (Reticule) defaultReticule.Duplicate();
        defaultReticule.GetParent().AddChild(reticule);
        return reticule.Controller;
    }

    public override void _Ready()
    {
        base._Ready();
        Controller.AddTo(this);
        defaultReticule.Visible = false; // Hide the default reticule
    }

    public IObservable<Unit> OnNextFrameAsObservable()
    {
        return new NextFrameProvider().OnNextFrameAsObservable();
    }
}

public class AbilityDialogController(IAbilityDialog implementation) : IAbilityDialogController
{
    private List<IReticuleController> reticules = new();
    private Subject<ISelectedCombatAbility> onEffectCompletedSubject = new();
    private CompositeDisposable disposables = new CompositeDisposable();
    private CompositeDisposable oneAbilitiesDisposable = new CompositeDisposable();


    public void DisplayAbilityDialog(ISelectedCombatAbility combatAbility, List<IUndo> undoList,
        List<IBattleCharacterController> team,
        List<IMonsterController> monsters, IObservable<Unit> backButtonSource)
    {
        //1. for each effect in the ability that needs a target
        //2 create a reticules for the effect
        //3. when the reticules is clicked, targets that can be targeted need to be highlighted
        //4. when a target that can be targeted is clicked, apply the effect to the target
        //5. get the effect so it can be undone if needed
        CreateReticules(combatAbility, undoList, team, monsters, backButtonSource);

        ListenForAllReticulesCompleted(combatAbility, undoList);
    }

    private void ListenForAllReticulesCompleted(ISelectedCombatAbility combatAbility, List<IUndo> undoList)
    {
        reticules.Select(r => r.OnCompleteAsObservable()).WhenAll().Subscribe(_ =>
        {
            combatAbility.GetCaster().SetHasActionAndUi(false);
            undoList.Add(new Undo(() => combatAbility.GetCaster().SetHasActionAndUi(true), "return action", false));
            ClearDialog();
        }).AddTo(oneAbilitiesDisposable);
    }

    public IObservable<ISelectedCombatAbility> OnEffectCompletedAsObservable()
    {
        return onEffectCompletedSubject.AsObservable();
    }

    /// <summary>
    /// also resets oneAbilitiesDisposable
    /// </summary>
    public void ClearDialog()
    {
        reticules.ForEach(r => r.Destroy());
        reticules.Clear();
        oneAbilitiesDisposable.Dispose();
        oneAbilitiesDisposable = new CompositeDisposable();
        disposables.Add(oneAbilitiesDisposable);
    }

    public int GetReticuleCount()
    {
        return reticules.Count;
    }

    public void CreateReticules(ISelectedCombatAbility combatAbility, List<IUndo> undoList, List<IBattleCharacterController> team,
        List<IMonsterController> monsters, IObservable<Unit> backButtonSource)
    {
        undoList.Add(new Undo(() =>
            {
                if (reticules.All(r => r.IsTextureVisible()))
                {
                    monsters.ForEach(m => m.HideOutline());
                    team.ForEach(c => c.HideOutline());
                    ClearDialog();
                }
            }, "if all reticules are visible, clearDialog",
            () => reticules.All(r => !r.IsTextureVisible()))); // todo return action to caster

        ClearDialog();
        foreach (ICombatAbilityEffect effect in combatAbility.GetEffects())
        {
            if (effect.GetTargetsSelf())
            {
                undoList.Add(effect.ApplyTo(combatAbility.GetCaster()));
                continue;
            }

            IReticuleController reticule = implementation.CreateBlankReticule();
            // return;
            reticules.Add(reticule);
            reticule.SetEffect(effect, undoList, team, monsters);
            reticule.OnCompleteAsObservable().Subscribe(_ => { onEffectCompletedSubject.OnNext(combatAbility); });
        }

        new ToggleButtonGroup(reticules).AddTo(oneAbilitiesDisposable);
        reticules.First().Select();
        
        reticules.Select(r=>r.OnCompleteAsObservable())
            .Merge()
            .SkipFrame(1, implementation)
            .Subscribe(_ =>
        {
            // when a reticule completes, select the left most reticule
            IReticuleController leftMostReticule = reticules.FirstOrNull(r => r.IsTextureVisible());
            leftMostReticule?.Select();
        }).AddTo(oneAbilitiesDisposable);

    }

    public void Dispose()
    {
    }
}

public interface IAbilityDialogController : IDisposable
{
    public void DisplayAbilityDialog(ISelectedCombatAbility combatAbility,
        List<IUndo> undoList,
        List<IBattleCharacterController> team,
        List<IMonsterController> monsters,
        IObservable<Unit> backButtonSource);

    IObservable<ISelectedCombatAbility> OnEffectCompletedAsObservable();
    void ClearDialog();
    int GetReticuleCount();
}

public interface IAbilityDialog: IFrameProvider
{
    IReticuleController CreateBlankReticule();
}
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.DescriptionToolTipScene;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Travel;

public partial class Travel : Control, ITravel
{
    private TravelController controller;
    public ITravelController Controller => controller ??= new TravelController(this);
    [Export] private TextureButton fishingButton;
    [Export] private TextureButton huntingButton;
    [Export] private TextureButton woodCuttingButton;
    [Export] private TextureButton relicsHuntingButton;
    [Export] private TextureButton craftingButton;
    [Export] private TextureButton goldCollectingButton;
    [Export] private TextureButton cookingButton;

    [Export] private DescriptionToolTip descriptionToolTip;

    [Export] private Godot.Collections.Array<TextureButton> routeButtons;
    [Export] private Godot.Collections.Array<TextureRect> characterTextureRects;

    private Subject<IRoute> routeSelectedSubject = new();


    public override void _Ready()
    {
        base._Ready();
        routeButtons.ForEach(button => { button.SetVisible(false); });
        descriptionToolTip.Set("", "");
        descriptionToolTip.SetVisible(false);
        routeButtons.ForEach(b => b.SetVisible(false));
        Controller.Ready();
        Controller.AddTo(this);
    }

    public IObservable<IRoute> OnRouteSelectedAsObservable()
    {
        return routeSelectedSubject.AsObservable();
    }

    public IRoute LoadRoute(Routes currentLocation)
    {
        return Route.LoadRoute(currentLocation);
    }

    public ISave GetSave()
    {
        return GameStateManager.Instance;
    }

    public void CreateRouteButton(IRoute route, out IObservable<bool> observable)
    {
        TextureButton routeButton = routeButtons.First(b => b.IsVisible() == false);
        routeButton.SetTextureNormal(route.GetRouteImage());
        routeButton.SetTextureHover(route.GetRouteImage());
        observable = routeButton.OnMouseEnterAsObservable().Select(_ => true)
            .Merge(routeButton.OnMouseExitAsObservable().Select(_ => false));
        routeButton.OnButtonPressedAsObservable().Subscribe(_ => routeSelectedSubject.OnNext(route))
            .AddTo(routeButton);
        routeButton.SetVisible(true);
    }

    public ulong MakeSeed()
    {
        return GD.Randi();
    }

    public void AutoSave()
    {
        GameStateManager.Instance.AutoSave();
    }

    public IDescriptionToolTip GetDescriptionToolTip()
    {
        return descriptionToolTip;
    }

    public void SetToolTipVisible(bool b)
    {
        descriptionToolTip.SetVisible(b);
    }
}

public partial class TravelController(ITravel implementation) : ITravelController
{
    private CompositeDisposable disposables = new();
    private IRoute currentRoute;

    public void Ready()
    {
        ISave  save            = implementation.GetSave();
        Routes currentLocation = save.GetKey<Routes>(nameof(ISave.Keys.CurrentRoute));
        currentRoute = implementation.LoadRoute(currentLocation);

        if (currentRoute.GetPossibleDestinations().Length == 0)
        {
            // do something idk
            return;
        }


        SetUpCampActivities(currentRoute);
        DisplayTeam(save);
        SetUpNextRoutes(currentRoute);
        // Initialize any necessary components or state here
    }

    public void SetUpCampActivities(IRoute route)
    {
        SetUpCooking(route);
        SetUpCrafting(route);
        SetUpFishing(route);
        SetUpHunting(route);
        SetUpWoodCutting(route);
        SetUpRelicsHunting(route);
        SetUpGoldCollecting(route);
    }


    public void Dispose()
    {
        disposables?.Dispose();
    }

    private async Task BeginNextRoute(IRoute route)
    {
        ISave save = implementation.GetSave();
        ulong seed = implementation.MakeSeed();
        save.SetKey(nameof(ISave.Keys.RngSeed),       seed);
        save.SetKey(nameof(ISave.Keys.CurrentRoute), route.GetRouteType());
        save.SetKey(nameof(ISave.Keys.Arrived),      false);
        implementation.AutoSave();

        ISceneManager sm = SceneManager.Instance;
        sm.ChangeScene(ISceneManager.SceneType.Battle);
    }
}

public interface ITravelController : IDisposable
{
    void Ready();
}

public partial interface ITravel
{
    IRoute LoadRoute(Routes currentLocation);
    ISave  GetSave();
    void   CreateRouteButton(IRoute route, out IObservable<bool> observable);
    ulong  MakeSeed();
    void   AutoSave();
}
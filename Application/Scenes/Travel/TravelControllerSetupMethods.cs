using System.Reactive.Linq;
using BringMeTreasure.DescriptionToolTipScene;
using BringMeTreasure.Extensions;
using BringMeTreasure.GodotAsync;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Travel;

public partial class TravelController
{
    public void SetUpCrafting(IRoute route)
    {
        // Implement crafting activities setup logic here
    }

    public void SetUpFishing(IRoute route)
    {
    }

    public void SetUpHunting(IRoute route)
    {
        // Implement hunting activities setup logic here
    }

    public void SetUpWoodCutting(IRoute route)
    {
        // Implement gathering activities setup logic here
    }

    public void SetUpRelicsHunting(IRoute route)
    {
        // Implement gathering activities setup logic here
    }

    public void SetUpGoldCollecting(IRoute route)
    {
    }

    public void SetUpCooking(IRoute route)
    {
    }

    public void SetUpNextRoutes(IRoute routeWhereWeAre)
    {
        Routes[]            rs                 = routeWhereWeAre.GetPossibleDestinations();
        IRoute[]            nextRoutes         = rs.Select(implementation.LoadRoute).ToArray();
        IDescriptionToolTip descriptionToolTip = implementation.GetDescriptionToolTip();
        nextRoutes.ForEach(r =>
        {
            implementation.CreateRouteButton(r, out IObservable<bool> hoverObservable);
            hoverObservable.Subscribe(isHovered =>
            {
                if (isHovered)
                {
                    implementation.SetToolTipVisible(true);
                    descriptionToolTip.Set(r.GetRouteName(), r.GetRouteDescription());
                }
                else
                {
                    implementation.SetToolTipVisible(false);
                }
            }).AddTo(disposables);
        });


        implementation.OnRouteSelectedAsObservable()
            .Take(1)
            .Subscribe(r => BeginNextRoute(r).Forget())
            .AddTo(disposables);
    }

    public void DisplayTeam(ISave save)
    {
    }
}

public partial interface ITravel
{
    IDescriptionToolTip GetDescriptionToolTip();
    void                SetToolTipVisible(bool b);
    IObservable<IRoute> OnRouteSelectedAsObservable();
}
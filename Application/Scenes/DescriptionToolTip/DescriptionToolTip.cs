using Godot;

namespace BringMeTreasure.DescriptionToolTipScene;

public partial class DescriptionToolTip : Control, IDescriptionToolTip
{
    [Export] private Label itemNameLabel;

    [Export] private RichTextLabel itemDescriptionLabel;

    [Export] private Panel line;

    public override void _Ready()
    {
        Set(String.Empty, string.Empty);
    }

    public override void _Process(double delta)
    {
        base._Process(delta);
        if (Visible)
        {
            Vector2 mousePosition = GetViewport().GetMousePosition();
            GlobalPosition = mousePosition;
        }
    }

    public void Set(string name, string description)
    {
        itemDescriptionLabel.SetText(description);
        itemDescriptionLabel.UpdateMinimumSize();

        if (name == String.Empty)
        {
            itemNameLabel.SetVisible(false);
            line.SetVisible(false);
            SetSize(itemDescriptionLabel.GetMinimumSize());
        }
        else
        {
            itemNameLabel.SetVisible(true);
            line.SetVisible(true);
            itemNameLabel.SetText(name);
            itemNameLabel.UpdateMinimumSize();
            SetSize(itemDescriptionLabel.GetMinimumSize() + itemNameLabel.GetMinimumSize() + line.GetMinimumSize());
        }
    }
}

public interface IDescriptionToolTip
{
    void Set(string name, string description);
}
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.DescriptionToolTipScene;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Map;

public partial class ItemSelectorDialog : Control, IItemSelectorDialog
{
    private ItemSelectorDialogController controller;
    public IItemSelectorDialogController Controller => controller ??= new ItemSelectorDialogController(this);
    [Export] private Button originalItemButton;
    [Export] private Button closeWithoutSelectionButton;
    [Export] private DescriptionToolTip toolTip;

    public override void _Ready()
    {
        base._Ready();
        Controller.AddTo(this);
        toolTip.Set("", "");
        toolTip.Visible            = false;
        originalItemButton.Visible = false;
        SetVisible(false);
    }

    public IDescriptionToolTip GetToolTip()
    {
        return toolTip;
    }

    public IDisposable CreateItemButton(IDisplayItem item, int count, out IObservable<bool> hovered,
        out IObservable<Unit> pressed)
    {
        Button    duplicateButton = originalItemButton.Duplicate() as Button;
        Texture2D itemIcon        = ResourceLoader.Load<Texture2D>(item.GetItemIconPath());
        duplicateButton.Icon = itemIcon;
        Label amountLabel = duplicateButton.GetChild<Label>(0);
        amountLabel.Text = count.ToString();

        if (count <= 1)
        {
            amountLabel.Visible = false;
        }


        originalItemButton.GetParent().AddChild(duplicateButton);
        duplicateButton.Visible = true;
        hovered = duplicateButton.OnMouseEnterAsObservable()
            .Select(_ => true)
            .Merge(duplicateButton.OnMouseExitAsObservable().Select(_ => false));
        pressed = duplicateButton.OnButtonPressedAsObservable();


        return Disposable.Create(() =>
        {
            if (duplicateButton.IsInsideTree())
            {
                duplicateButton.QueueFree();
            }
        });
    }

    public void SetToolTipVisible(bool b)
    {
        toolTip.Visible = b;
    }

    public void CreateBlank(out IObservable<Unit> observable, out IDisposable disposable)
    {
        Button blankButton = originalItemButton.Duplicate() as Button;
        blankButton.Icon = null;
        originalItemButton.GetParent().AddChild(blankButton);
        blankButton.Visible = true;

        observable = blankButton.OnButtonPressedAsObservable();
        disposable = Disposable.Create(() =>
        {
            if (blankButton.IsInsideTree())
            {
                blankButton.QueueFree();
            }
        });
    }

    public IObservable<Unit> OnCloseWithoutSelectionButtonPressedAsObservable()
    {
        return closeWithoutSelectionButton.OnButtonPressedAsObservable()
            .AsObservable();
    }
}

public class ItemSelectorDialogController(IItemSelectorDialog implementation) : IItemSelectorDialogController
{
    private CompositeDisposable disposables = new();
    private CompositeDisposable aDisplayDisposables = new();


    public IObservable<IDisplayItem> CreateItems(IEnumerable<(IDisplayItem, int)> items)
    {
        Subject<IDisplayItem> itemSelectedSubject = new();
        IDescriptionToolTip   toolTip             = implementation.GetToolTip();
        aDisplayDisposables.Clear();

        int itemsCreated = 0;
        foreach ((IDisplayItem, int) i in items)
        {
            itemsCreated++;
            (IDisplayItem item, int count) = i;

            IDisposable queueFreeDisposable = implementation.CreateItemButton(item,
                count, out IObservable<bool> hoverObservable, out IObservable<Unit> pressedObservable);

            aDisplayDisposables.Add(queueFreeDisposable);
            IDisposable hoverDisposable = hoverObservable
                .Subscribe(isHovered =>
                {
                    if (isHovered)
                    {
                        implementation.SetToolTipVisible(true);
                        toolTip.Set(item.GetItemName(), item.GetItemDescription());
                    }
                    else
                    {
                        implementation.SetToolTipVisible(false);
                    }
                });
            aDisplayDisposables.Add(hoverDisposable);
            IDisposable pressedDisposable = pressedObservable
                .Subscribe(_ =>
                {
                    itemSelectedSubject.OnNext(item);
                    itemSelectedSubject.OnCompleted();
                    implementation.SetToolTipVisible(false);
                });
            aDisplayDisposables.Add(pressedDisposable);
            implementation.SetVisible(false);
        }

        int slotsToFill = 35 - itemsCreated;
        if (slotsToFill < 0)
        {
            slotsToFill = 0;
            CreateBlank();
        }

        for (int i = 0;
             i < slotsToFill;
             i++)
        {
            CreateBlank();
        }

// add blanks until multiple of 5
        while (itemsCreated % 5 != 0)
        {
            CreateBlank();
        }

        implementation.SetVisible(true);

        implementation.OnCloseWithoutSelectionButtonPressedAsObservable()
            .Subscribe(_ =>
            {
                itemSelectedSubject.OnCompleted();
                implementation.SetVisible(false);
            }).AddTo(aDisplayDisposables);

        return itemSelectedSubject.Take(1)
            .AsObservable();

        void CreateBlank()
        {
            itemsCreated++;
            implementation.CreateBlank(
                out IObservable<Unit> blankOnClickAsObservable, out IDisposable blankQueueFreeDisposable);
            blankQueueFreeDisposable.AddTo(aDisplayDisposables);
            blankOnClickAsObservable.Subscribe(_ =>
            {
                itemSelectedSubject.OnNext(null);
                itemSelectedSubject.OnCompleted();
            }).AddTo(aDisplayDisposables);
        }
    }

    public void Dispose()
    {
        disposables?.Dispose();
        aDisplayDisposables?.Dispose();
    }
}

public interface IItemSelectorDialogController : IDisposable
{
    public IObservable<IDisplayItem> CreateItems(IEnumerable<(IDisplayItem, int)> items);
}

public interface IItemSelectorDialog
{
    IDescriptionToolTip GetToolTip();

    IDisposable CreateItemButton(IDisplayItem item, int count, out IObservable<bool> hovered,
        out IObservable<Unit> pressed);

    void              SetToolTipVisible(bool b);
    void              SetVisible(bool b);
    void              CreateBlank(out IObservable<Unit> observable, out IDisposable disposable);
    IObservable<Unit> OnCloseWithoutSelectionButtonPressedAsObservable();
}
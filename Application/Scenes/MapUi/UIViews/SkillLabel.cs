using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Map.UIViews;

public partial class SkillLabel : Control, ISkillLabel
{
    private SkillLabelController controller;
    public ISkillLabelController Controller => controller ??= new SkillLabelController(this);

    [Export] private Label skillNameLabel;
    [Export] private Label skillValueLabel;
    [Export] private Label toAddLabel;
    [Export] private Button upButton;
    [Export] private Button downButton;

    public void SetAddMinusButtonsVisible(bool b)
    {
        upButton.SetVisible(b);
        downButton.SetVisible(b);
        toAddLabel.SetVisible(b);
    }

    public void SetSkillText(string name)
    {
        skillNameLabel.Text = name;
    }

    public void SetSkillValue(int value)
    {
        skillValueLabel.Text = value.ToString();
    }

    public IObservable<bool> OnHoverAsObservable()
    {
        return this.OnMouseEnterAsObservable().Select(_ => true)
            .Merge(this.OnMouseExitAsObservable().Select(_ => false));
    }

    public override void _Ready()
    {
        base._Ready();
        Controller.AddTo(this);
    }
}

public class SkillLabelController(ISkillLabel implementation) : ISkillLabelController
{
    private readonly CompositeDisposable disposables = new();

    private Subject<(SkillType, int)> skillTypeAndNewTotal = new();

    private SkillType skillType;

    public void Initialize(ISkill skill)
    {
        skillType = skill.GetSkillType();
        implementation.SetAddMinusButtonsVisible(false);
        implementation.SetSkillText(skill.GetName() + ":");
        implementation.SetSkillValue(skill.GetWeight());
    }


    public void EnableLevelUpDialog()
    {
        implementation.SetAddMinusButtonsVisible(true);
    }

    public void HideLevelUpDialog()
    {
        // Hide the level up dialog if it is visible
    }


    public void Dispose()
    {
        disposables?.Dispose();
    }

    public IObservable<bool> OnHoverAsObservable()
    {
        return implementation.OnHoverAsObservable();
    }
}

public interface ISkillLabelController : IDisposable
{
    IObservable<bool> OnHoverAsObservable();
    void Initialize(ISkill skill);
}

public interface ISkillLabel
{
    void SetAddMinusButtonsVisible(bool b);
    void SetSkillText(string name);
    void SetSkillValue(int value);
    IObservable<bool> OnHoverAsObservable();
}
using System.Reactive.Disposables;
using System.Reactive.Linq;
using BringMeTreasure.Behaviors;
using BringMeTreasure.DescriptionToolTipScene;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;
using Godot;

namespace BringMeTreasure.Map.UIViews;

public class ASingleCharacterDisplay(ICharacterView view, ICharacterViewController controller)
    : IASingleCharacterDisplay
{
    private readonly CompositeDisposable disposables = new();
    ICharacterData characterData;
    ToggleButtonGroup toggleButtonGroup;


    public void DisplayCharacter(ICharacterData character)
    {
        characterData = character;
        ListenForSkillTabChanged(character);
        CloseEquipmentSelectorOnTabChanged();
        OpenEquipmentSelectorOnEquipmentButtonPressed();
        ListenForHoverOverEquippedItems();
        DisplayCharacterEquipment(character);
    }

    public void ListenForSkillTabChanged(ICharacterData character)
    {
        ITab[] skillTabs = view.CreateSkillTabs();

        ITab abilityTab = skillTabs[0];
        ITab talentTab  = skillTabs[1];
        ITab skillTab   = skillTabs[2];
        toggleButtonGroup = new ToggleButtonGroup(skillTabs);
        toggleButtonGroup.AddTo(disposables);

        abilityTab.OnTabSelectedAsObservable()
            .Subscribe(_ => controller.FillAbilityList(character.GetCombatAbilities()))
            .AddTo(disposables);
        talentTab.OnTabSelectedAsObservable().Subscribe(_ => controller.FillAbilityListTalents(character.GetTalents()))
            .AddTo(disposables);
        skillTab.OnTabSelectedAsObservable()
            .Subscribe(_ => controller.FillSkillList(character.GetSkills())).AddTo(disposables);
        skillTabs.First().SetPressed(true);
    }

    public void CloseEquipmentSelectorOnTabChanged()
    {
        Disposable.Create(() => { view.SetEquipmentSelectorVisible(false); }).AddTo(disposables);
    }

    public void OpenEquipmentSelectorOnEquipmentButtonPressed()
    {
        view.OnEquipmentButtonPressedAsObservable()
            .Subscribe(t =>
            {
                IObservable<IAbilityProviderItem> nextItemSelected = controller.FillEquipmentSelector(t.itemType);
                view.SetEquipmentSelectorVisible(true);
                nextItemSelected.Take(1).Subscribe(item =>
                {
                    IAbilityProviderItem currentItem = characterData.GetEquipment(t.itemType, t.quickSlotIndex);
                    IInventory           inventory   = view.GetGameState().GetInventory();
                    if (currentItem != null)
                    {
                        inventory.AddItem(currentItem);
                    }


                    if (item != null)
                    {
                        characterData.SetEquipment(t.itemType, item, t.quickSlotIndex);
                        view.SetEquipmentCharacterEquipment(t.itemType, item, t.quickSlotIndex);
                    }
                    else
                    {
                        characterData.RemoveEquipment(t.itemType, t.quickSlotIndex);
                        view.SetEquipmentCharacterEquipmentBlank(t.itemType, t.quickSlotIndex);
                    }

                    inventory.RemoveItem(item);
                    view.AutoSave();


                    view.SetEquipmentSelectorVisible(false);
                    toggleButtonGroup.ResendPressedSignal();
                }).AddTo(disposables);
            })
            .AddTo(disposables);
    }

    public void ListenForHoverOverEquippedItems()
    {
        view.OnEquippedItemOverAsObservable().Subscribe(t =>
        {
            ItemType itemType       = t.itemType;
            int      quickSlotIndex = t.quickSlotIndex;
            bool     isHovered      = t.isHovered;

            if (!isHovered)
            {
                view.SetToolTipVisible(false);
                return;
            }

            IAbilityProviderItem item = characterData.GetEquipment(itemType, quickSlotIndex);
            if (item == null)
            {
                view.SetToolTipVisible(false);
                return;
            }

            string itemName        = item.GetItemName();
            string itemDescription = item.GetItemDescription();

            IDescriptionToolTip tip = view.GetToolTip();
            tip.Set(itemName, itemDescription);
            view.SetToolTipVisible(true);
        }).AddTo(disposables);
    }

    public void DisplayCharacterEquipment(ICharacterData character)
    {
        foreach (ItemType itemType in Enum.GetValues(typeof(ItemType)))
        {
            IAbilityProviderItem item;
            if (itemType == ItemType.QuickItem)
            {
                for (int i = 0; i < 3; i++)
                {
                    item = character.GetEquipment(itemType, i);

                    if (item != null)
                    {
                        view.SetEquipmentCharacterEquipment(itemType, item, i);
                    }
                    else
                    {
                        view.SetEquipmentCharacterEquipmentBlank(itemType, i);
                    }
                }

                return;
            }

            item = character.GetEquipment(itemType, -1);
            if (item != null)
            {
                view.SetEquipmentCharacterEquipment(itemType, item, -1);
            }
            else
            {
                view.SetEquipmentCharacterEquipmentBlank(itemType, -1);
            }
        }
    }


    public void Dispose()
    {
        disposables.Dispose();
    }
}

public interface IASingleCharacterDisplay : IDisposable
{
}
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Application.Items.Relics;
using BringMeTreasure.Behaviors;
using BringMeTreasure.DescriptionToolTipScene;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;
using Godot;

namespace BringMeTreasure.Map.UIViews;

/// <summary>
///this manages which character is displayed. Not displaying a character;
/// </summary>
public partial class CharacterView : Control, ICharacterView
{
    private CharacterViewController controller;
    public ICharacterViewController Controller => controller ??= new CharacterViewController(this);

    [Export] private Godot.Collections.Array<Button> tabs;
    [Export] private Godot.Collections.Array<Button> skillViewTabs;
    [Export] private Control abilityListLabelParent; // clone by cloning its parent
    [Export] private Button talentListButton;
    [Export] private SkillLabel skillLabel;
    [Export] private DescriptionToolTip toolTip;
    [Export] private Control equipmentSelector;
    [Export] private Button originalEquipmentSelectorButton;
    [Export] private Button equipmentSelectorCloseButton;

    //inventory slots
    [Export] private Button weaponSlotButton;
    [Export] private Button helmetSlotButton;
    [Export] private Button tunicSlotButton;
    [Export] private Button bracersSlotButton;
    [Export] private Button greavesSlotButton;
    [Export] private Button backPackSlotButton;
    [Export] private Button quickSlotButton1;
    [Export] private Button quickSlotButton2;
    [Export] private Button quickSlotButton3;


    public override void _Ready()
    {
        base._Ready();
        Controller.AddTo(this);
        abilityListLabelParent.SetVisible(false);
        talentListButton.SetVisible(false);
        SetToolTipVisible(false);
        SetToolTipVisible(false);
        skillLabel.SetVisible(false);
        originalEquipmentSelectorButton.SetVisible(false);
    }

    public ITab[] CreateCharacterTabs()
    {
        return tabs.Select(t => new Tab(t)).ToArray();
    }

    public ITab[] CreateSkillTabs()
    {
        return skillViewTabs.Select(t => new Tab(t)).ToArray<ITab>();
    }

    public void CreateAbilityListLabel(string name, out IObservable<bool> onHoverAsObservable,
        out IDisposable queueFreeDisposable)
    {
        Control newParent = (Control) abilityListLabelParent.Duplicate();
        Label   newLabel  = newParent.GetChild<Label>(0);
        newLabel.Text = name;
        queueFreeDisposable = Disposable.Create(() =>
        {
            if (IsInstanceValid(newParent))
            {
                newParent.QueueFree();
            }
        });

        onHoverAsObservable = newParent.OnMouseEnterAsObservable().Select(_ => true)
            .Merge(newParent.OnMouseExitAsObservable().Select(_ => false));

        abilityListLabelParent.GetParent().AddChild(newParent);
        newParent.SetVisible(true);
    }

    public void CreateAbilityListLabel(string name, out IObservable<bool> onHoverAsObservable,
        out IObservable<Unit> onClickAsObservable,
        out IDisposable queueFreeDisposable)
    {
        Button newButton = (Button) talentListButton.Duplicate();
        newButton.Text = name;
        queueFreeDisposable = Disposable.Create(() =>
        {
            if (IsInstanceValid(newButton))
            {
                newButton.QueueFree();
            }
        });

        onHoverAsObservable = newButton.OnMouseEnterAsObservable().Select(_ => true)
            .Merge(newButton.OnMouseExitAsObservable().Select(_ => false));
        onClickAsObservable = newButton.OnButtonPressedAsObservable();

        talentListButton.GetParent().AddChild(newButton);
        newButton.SetVisible(true);
    }

    public void SetToolTipVisible(bool visible)
    {
        toolTip.SetVisible(visible);
    }

    public ISkillLabelController CreateSkillLabel(out IDisposable queueFreeDisposable)
    {
        SkillLabel newLabel = (SkillLabel) skillLabel.Duplicate();
        skillLabel.GetParent().AddChild(newLabel);
        newLabel.SetVisible(true);
        queueFreeDisposable = Disposable.Create(() =>
        {
            if (IsInstanceValid(newLabel))
            {
                newLabel.QueueFree();
            }
        });
        return newLabel.Controller;
    }

    public void SetEquipmentSelectorVisible(bool b)
    {
        equipmentSelector.SetVisible(b);
    }

    public IObservable<(ItemType, int)> OnEquipmentButtonPressedAsObservable()
    {
        return Observable.Merge(
        [
            helmetSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.Helmet, 0)),
            bracersSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.Bracers, 0)),
            greavesSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.Greaves, 0)),
            tunicSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.Tunic, 0)),
            backPackSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.BackPack, 0)),
            weaponSlotButton.OnButtonPressedAsObservable().Select(_ => (ItemType.Weapon, 0)),
            quickSlotButton1.OnButtonPressedAsObservable().Select(_ => (ItemType.QuickItem, 0)),
            quickSlotButton2.OnButtonPressedAsObservable().Select(_ => (ItemType.QuickItem, 1)),
            quickSlotButton3.OnButtonPressedAsObservable().Select(_ => (ItemType.QuickItem, 2))
        ]);
    }

    public void SetEquipmentCharacterEquipment(ItemType objItemType, IAbilityProviderItem item, int objQuickSlotIndex)
    {
        Texture2D icon = GD.Load<Texture2D>(item.GetItemIconPath());
        switch (objItemType)
        {
            case ItemType.Helmet:
                helmetSlotButton.Icon = icon;
                break;
            case ItemType.Bracers:
                bracersSlotButton.Icon = icon;
                break;
            case ItemType.Greaves:
                greavesSlotButton.Icon = icon;
                break;
            case ItemType.Tunic:
                tunicSlotButton.Icon = icon;
                break;
            case ItemType.BackPack:
                backPackSlotButton.Icon = icon;
                break;
            case ItemType.Weapon:
                weaponSlotButton.Icon = icon;
                break;
            case ItemType.QuickItem:
                switch (objQuickSlotIndex)
                {
                    case 0:
                        quickSlotButton1.Icon = icon;
                        break;
                    case 1:
                        quickSlotButton2.Icon = icon;
                        break;
                    case 2:
                        quickSlotButton3.Icon = icon;
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(objQuickSlotIndex), objQuickSlotIndex, null);
                }

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(objItemType), objItemType, null);
        }
    }

    public ISave GetGameState()
    {
        return GameStateManager.Instance;
    }

    public void AutoSave()
    {
        GameStateManager.Instance.AutoSave();
    }

    public void SetEquipmentCharacterEquipmentBlank(ItemType itemType, int quickItemIndex)
    {
        switch (itemType)
        {
            case ItemType.Helmet:
                helmetSlotButton.Icon = null;
                break;
            case ItemType.Bracers:
                bracersSlotButton.Icon = null;
                break;
            case ItemType.Greaves:
                greavesSlotButton.Icon = null;
                break;
            case ItemType.Tunic:
                tunicSlotButton.Icon = null;
                break;
            case ItemType.BackPack:
                backPackSlotButton.Icon = null;
                break;
            case ItemType.Weapon:
                weaponSlotButton.Icon = null;
                break;
            case ItemType.QuickItem:
                switch (quickItemIndex)
                {
                    case 0:
                        quickSlotButton1.Icon = null;
                        break;
                    case 1:
                        quickSlotButton2.Icon = null;
                        break;
                    case 2:
                        quickSlotButton3.Icon = null;
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(quickItemIndex), quickItemIndex, null);
                }

                break;
        }
    }

    public void CreateABlankEquipmentSelectorButton(out IObservable<Unit> observable, out IDisposable disposable)
    {
        Button newButton = (Button) originalEquipmentSelectorButton.Duplicate();
        newButton.Icon = null;
        disposable = Disposable.Create(() =>
        {
            if (IsInstanceValid(newButton))
            {
                newButton.QueueFree();
            }
        });

        observable = newButton.OnButtonPressedAsObservable();

        originalEquipmentSelectorButton.GetParent().AddChild(newButton);
        newButton.SetVisible(true);
    }

    public void CreateAEquipmentSelectorButton(IDisplayItem displayItem, out IObservable<bool> observable,
        out IObservable<Unit> observable1,
        out IDisposable disposable)
    {
        Button newButton = (Button) originalEquipmentSelectorButton.Duplicate();
        newButton.Icon = GD.Load<Texture2D>(displayItem.GetItemIconPath());
        disposable = Disposable.Create(() =>
        {
            if (IsInstanceValid(newButton))
            {
                newButton.QueueFree();
            }
        });

        observable = newButton.OnMouseEnterAsObservable().Select(_ => true)
            .Merge(newButton.OnMouseExitAsObservable().Select(_ => false));
        observable1 = newButton.OnButtonPressedAsObservable();

        originalEquipmentSelectorButton.GetParent().AddChild(newButton);
        newButton.SetVisible(true);
    }

    public IObservable<Unit> OnEquipmentSelectorCloseButtonPressedAsObservable()
    {
        return equipmentSelectorCloseButton.OnButtonPressedAsObservable();
    }

    public IDescriptionToolTip GetToolTip()
    {
        return toolTip;
    }

    public IObservable<(ItemType itemType, int quickSlotIndex, bool isHovered)> OnEquippedItemOverAsObservable()
    {
        return Observable.Merge(
        [
            helmetSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.Helmet, 0, true)),
            bracersSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.Bracers, 0, true)),
            greavesSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.Greaves, 0, true)),
            tunicSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.Tunic, 0, true)),
            backPackSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.BackPack, 0, true)),
            weaponSlotButton.OnMouseEnterAsObservable().Select(_ => (ItemType.Weapon, 0, true)),
            quickSlotButton1.OnMouseEnterAsObservable().Select(_ => (ItemType.QuickItem, 0, true)),
            quickSlotButton2.OnMouseEnterAsObservable().Select(_ => (ItemType.QuickItem, 1, true)),
            quickSlotButton3.OnMouseEnterAsObservable().Select(_ => (ItemType.QuickItem, 2, true)),
            helmetSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.Helmet, 0, false)),
            bracersSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.Bracers, 0, false)),
            greavesSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.Greaves, 0, false)),
            tunicSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.Tunic, 0, false)),
            backPackSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.BackPack, 0, false)),
            weaponSlotButton.OnMouseExitAsObservable().Select(_ => (ItemType.Weapon, 0, false)),
            quickSlotButton1.OnMouseExitAsObservable().Select(_ => (ItemType.QuickItem, 0, false)),
            quickSlotButton2.OnMouseExitAsObservable().Select(_ => (ItemType.QuickItem, 1, false)),
            quickSlotButton3.OnMouseExitAsObservable().Select(_ => (ItemType.QuickItem, 2, false)),
        ]);
    }

    public ICharacterData[] GetCharacters()
    {
        // for (int i = 0; i < 37; i++)
        // {
        //     GameStateManager.Instance.GetInventory().AddItem(new CraftedItem("Test Item2", "This is a test item",
        //         "res://Application/Items/4x/armor and clothing/blue hood 4x.png", ItemType.Weapon,
        //         [new BasicAttackRelic()]));
        // }
        // GameStateManager.Instance.GetInventory().AddItem(new CraftedItem("Test Item2", "This is a test item",
        //     "res://Application/Items/4x/armor and clothing/blue dress 4x.png", ItemType.Weapon,
        //     [new BasicAttackRelic()]));

        // GameStateManager.Instance.SetParty([
        //     Raider.Create(),
        //     Raider.Create(),
        //     Raider.Create(),
        //     Raider.Create()
        // ]);
        //
        // GameStateManager.Instance.AutoSave();

        return GameStateManager.Instance.GetParty();
    }
}

public class CharacterViewController : ICharacterViewController
{
    private CompositeDisposable disposables = new();
    private CompositeDisposable abilityListDisposables = new();
    private CompositeDisposable equipmentSelectorDisposables = new();
    private readonly ICharacterView implementation;


    public CharacterViewController(ICharacterView implementation)
    {
        this.implementation = implementation;
        ListenForCharacterTabChanged();
        CloseEquipmentSelectorOnCloseButtonPressed();
    }

    public void CloseEquipmentSelectorOnCloseButtonPressed()
    {
        implementation.OnEquipmentSelectorCloseButtonPressedAsObservable()
            .Subscribe(_ =>
            {
                implementation.SetEquipmentSelectorVisible(false);
                equipmentSelectorDisposables?.Dispose();
            })
            .AddTo(disposables);
    }

    public void ListenForCharacterTabChanged()
    {
        ITab[] tabs = implementation.CreateCharacterTabs();
        new ToggleButtonGroup(tabs).AddTo(disposables);
        ASingleCharacterDisplay display = null;


        ICharacterData[] characters = implementation.GetCharacters();
        tabs.Skip(characters.Length).ForEach(t => t.SetVisible(false));
        for (int i = 0; i < characters.Length; i++)
        {
            int index = i; // Capture the current index for the lambda
            tabs[i].SetTabName(characters[i].GetName());
            tabs[i].OnTabSelectedAsObservable().Subscribe(_ =>
            {
                display?.Dispose();
                display = new ASingleCharacterDisplay(implementation, this);
                display.DisplayCharacter(characters[index]);
                display.AddTo(disposables);
            });
        }

        tabs.First().SetPressed(true);
    }

    public void Dispose()
    {
        disposables.Dispose();
    }

    public void FillAbilityList(IAbility[] abilities)
    {
        abilityListDisposables.Dispose();
        abilityListDisposables = new CompositeDisposable();
        foreach (IAbility ability in abilities)
        {
            implementation.CreateAbilityListLabel(ability.GetName(), out IObservable<bool> onHoverAsObservable,
                out IDisposable queueFreeDisposable);
            abilityListDisposables.Add(queueFreeDisposable);

            ShowDescriptionOnAbilityHover(ability, onHoverAsObservable).AddTo(abilityListDisposables);
        }
    }

    public void FillAbilityListTalents(ITalent[] talents)
    {
        abilityListDisposables.Dispose();
        abilityListDisposables = new CompositeDisposable();
        foreach (ITalent talent in talents)
        {
            implementation.CreateAbilityListLabel(talent.GetName(), out IObservable<bool> onHoverAsObservable,
                out IObservable<Unit> onClickAsObservable, out IDisposable queueFreeDisposable);
            abilityListDisposables.Add(queueFreeDisposable);

            ShowDescriptionOnAbilityHover(talent, onHoverAsObservable).AddTo(abilityListDisposables);
            onClickAsObservable.Subscribe(_ => { }); // todo: show talent dialog
        }
    }

    public ISkillLabelController[] FillSkillList(ISkill[] skills)
    {
        abilityListDisposables.Dispose();
        abilityListDisposables = new CompositeDisposable();
        List<ISkillLabelController> skillLabelControllers = new();
        foreach (ISkill skill in skills)
        {
            ISkillLabelController skillLabelController =
                implementation.CreateSkillLabel(out IDisposable queueFreeDisposable);
            skillLabelController.Initialize(skill);
            abilityListDisposables.Add(skillLabelController);
            abilityListDisposables.Add(queueFreeDisposable);

            ShowDescriptionOnAbilityHover(skill, skillLabelController.OnHoverAsObservable())
                .AddTo(abilityListDisposables);
            skillLabelControllers.Add(skillLabelController);
        }
        return skillLabelControllers
            .ToArray();
    }


    public IObservable<IAbilityProviderItem> FillEquipmentSelector(ItemType objItemType)
    {
        equipmentSelectorDisposables?.Dispose();
        equipmentSelectorDisposables = new CompositeDisposable();
        IInventory          inventory          = implementation.GetGameState().GetInventory();
        IDescriptionToolTip descriptionToolTip = implementation.GetToolTip();

        int itemsCreated = 0;

        List<IAbilityProviderItem> itemsToDisplay =
            inventory.GetCraftedItems().Where(i => i.GetItemType() == objItemType).ToList();
        if (objItemType == ItemType.QuickItem) // add weapons to quick items
        {
            itemsToDisplay.AddRange(inventory.GetCraftedItems()
                .Where(i => i.GetItemType() == ItemType.Weapon));
        }


        Subject<IAbilityProviderItem> itemSelectedSubject =
            new Subject<IAbilityProviderItem>();
        foreach (IDisplayItem displayItem in itemsToDisplay)
        {
            itemsCreated++;
            implementation.CreateAEquipmentSelectorButton(displayItem, out IObservable<bool> onHoverAsObservable,
                out IObservable<Unit> onClickAsObservable,
                out IDisposable queueFreeDisposable);

            queueFreeDisposable.AddTo(equipmentSelectorDisposables);
            onHoverAsObservable.Subscribe(hovered =>
            {
                if (hovered)
                {
                    descriptionToolTip.Set(displayItem.GetItemName(), displayItem.GetItemDescription());
                    implementation.SetToolTipVisible(true);
                }
                else
                {
                    implementation.SetToolTipVisible(false);
                }
            }).AddTo(equipmentSelectorDisposables);
            onClickAsObservable.Subscribe(_ =>
            {
                itemSelectedSubject.OnNext((IAbilityProviderItem) displayItem);
                itemSelectedSubject.OnCompleted();
            }).AddTo(equipmentSelectorDisposables);
        }

        // minimum 35 slots
        int slotsToFill = 35 - itemsCreated;
        if (slotsToFill < 0)
        {
            slotsToFill = 0;
            CreateBlank();
        }

        for (int i = 0; i < slotsToFill; i++)
        {
            CreateBlank();
        }

        // add blanks until multiple of 5
        while (itemsCreated % 5 != 0)
        {
            CreateBlank();
        }

        return itemSelectedSubject
            .Take(1)
            .AsObservable();

        void CreateBlank()
        {
            itemsCreated++;
            implementation.CreateABlankEquipmentSelectorButton(
                out IObservable<Unit> blankOnClickAsObservable, out IDisposable blankQueueFreeDisposable);
            blankQueueFreeDisposable.AddTo(equipmentSelectorDisposables);
            blankOnClickAsObservable.Subscribe(_ =>
            {
                itemSelectedSubject.OnNext(null);
                itemSelectedSubject.OnCompleted();
            }).AddTo(equipmentSelectorDisposables);
        }
    }

    private IDisposable ShowDescriptionOnAbilityHover(IAbility ability, IObservable<bool> onHoverAsObservable)
    {
        IDescriptionToolTip descriptionToolTip = implementation.GetToolTip();
        return onHoverAsObservable.Subscribe(hovered =>
        {
            if (hovered)
            {
                descriptionToolTip.Set(string.Empty, ability.GetDescription());
                implementation.SetToolTipVisible(true);
            }
            else
            {
                implementation.SetToolTipVisible(false);
            }
        });
    }
}

public interface ICharacterViewController : IDisposable
{
    void                              FillAbilityList(IAbility[] abilities);
    void                              FillAbilityListTalents(ITalent[] talents);
    ISkillLabelController[]           FillSkillList(ISkill[] skills);
    IObservable<IAbilityProviderItem> FillEquipmentSelector(ItemType objItemType);
}

public interface ICharacterView
{
    ITab[]           CreateCharacterTabs();
    ICharacterData[] GetCharacters();
    ITab[]           CreateSkillTabs();

    void CreateAbilityListLabel(string name, out IObservable<bool> onHoverAsObservable,
        out IDisposable queueFreeDisposable);

    void CreateAbilityListLabel(string name, out IObservable<bool> onHoverAsObservable,
        out IObservable<Unit> onClickAsObservable, out IDisposable queueFreeDisposable);

    void                  SetToolTipVisible(bool visible);
    ISkillLabelController CreateSkillLabel(out IDisposable queueFreeDisposable);
    void                  SetEquipmentSelectorVisible(bool b);

    /// <summary>
    /// int is quickItem index
    /// </summary>
    /// <returns></returns>
    IObservable<(ItemType itemType, int quickSlotIndex)> OnEquipmentButtonPressedAsObservable();

    void  SetEquipmentCharacterEquipment(ItemType objItemType, IAbilityProviderItem item, int objQuickSlotIndex);
    ISave GetGameState();

    void CreateAEquipmentSelectorButton(IDisplayItem displayItem, out IObservable<bool> observable,
        out IObservable<Unit> observable1, out IDisposable disposable);

    IObservable<Unit>   OnEquipmentSelectorCloseButtonPressedAsObservable();
    IDescriptionToolTip GetToolTip();

    IObservable<(ItemType itemType, int quickSlotIndex, bool isHovered)>
        OnEquippedItemOverAsObservable();

    void AutoSave();
    void SetEquipmentCharacterEquipmentBlank(ItemType itemType, int quickItemIndex);
    void CreateABlankEquipmentSelectorButton(out IObservable<Unit> observable, out IDisposable disposable);
}
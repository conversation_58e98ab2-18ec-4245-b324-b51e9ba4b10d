using System.Reactive;
using BringMeTreasure.Application.Scripts;
using BringMeTreasure.Extensions;
using BringMeTreasure.Map.UIViews;
using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Map;

public partial class MapUi : Control, IMapUi
{
    private MapUiController controller;
    public IMapUiController Controller => controller ??= new MapUiController(this);
    [Export] private Label depthLabel;
    [Export] private Label moneyLabel;
    [Export] private Label hourLabel;
    [Export] private Button travelButton;
    [Export] private CharacterView characterView;
    // [Export] private Button inventoryButton;

    public override void _Ready()
    {
        base._Ready();
        Controller.Ready();
        Controller.AddTo(this);
        new MoneyDisplay(moneyLabel, GameStateManager.Instance.GetInventory()).AddTo(this);
        characterView.SetVisible(false);
    }

    public ITranslate GetTranslate()
    {
        return new Translate();
    }

    public void SetDepthLabel(string text)
    {
        depthLabel.Text = text;
    }

    public void SetHourLabel(string time)
    {
        hourLabel.SetText(time);
    }

    public void SetTravelButtonEnabled(bool canTravel)
    {
        travelButton.Disabled = !canTravel;
    }

    public IObservable<Unit> OnTravelButtonPressedAsObservable()
    {
        return travelButton.OnButtonPressedAsObservable();
    }
}

public class MapUiController(IMapUi implementation) : IMapUiController
{
    public void Dispose()
    {
    }

    public void Ready()
    {
        // throw new NotImplementedException();
    }

    public void SetDepth(int depth)
    {
        string raw = implementation.GetTranslate().Tr("MapDepthDisplay");

        // find depth
        Dictionary<string, int> depths = WorldConstants.Depths;
        List<KeyValuePair<string, int>> depthList = depths.OrderBy(x => x.Value).ToList();
        string depthName = depthList.FirstOrDefault(x => x.Value >= depth).Key;
        raw = raw.Replace("{depthName}", depthName);
        raw = raw.Replace("{depthValue}", depth.ToString());
        implementation.SetDepthLabel(raw);
    }

    public void SetHour(int hour)
    {
        implementation.SetHourLabel(TimeSpan.FromHours(hour).ToString(@"hh\:mm") + (hour > 12 ? " PM" : " AM"));
    }

    public void SetTravelButtonEnabled(bool canTravel)
    {
        implementation.SetTravelButtonEnabled(canTravel);
    }

    public IObservable<Unit> OnTravelButtonPressedAsObservable()
    {
        return implementation.OnTravelButtonPressedAsObservable();
    }
}

public interface IMapUiController : IDisposable
{
    void Ready();
    public void SetDepth(int depth);
    void SetHour(int hour);
    void SetTravelButtonEnabled(bool canTravel);
    public IObservable<Unit> OnTravelButtonPressedAsObservable();
}

public interface IMapUi
{
    ITranslate GetTranslate();
    void SetDepthLabel(string text);
    void SetHourLabel(string time);
    void SetTravelButtonEnabled(bool canTravel);
    IObservable<Unit> OnTravelButtonPressedAsObservable();
}
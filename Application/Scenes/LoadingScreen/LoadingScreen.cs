using Godot;
using System;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using BringMeTreasure.Extensions;

public partial class LoadingScreen : Control
{
    private static int ClearRequestCount = 0;
    private static int GreyRequestCount = 0;
    private static int DarkRequestCount = 0;
    private static LoadingScreen _instance;


    private void UpdateView()
    {
        if (DarkRequestCount > 0)
        {
            _instance.Modulate = new Color(0, 0, 0, 1f);
            return;
        }

        if (GreyRequestCount > 0)
        {
            _instance.Modulate = new Color(1f, 1f, 1f, .3f);
            return;
        }

        if (ClearRequestCount > 0)
        {
            _instance.Modulate = new Color(1f, 1f, 1f, 0f);
            return;
        }

        _instance.QueueFree();
    }


    public static IDisposable ShowClear()
    {
        ClearRequestCount++;
        if (_instance == null)
        {
            CreateScreen();
        }
        else
        {
            _instance.UpdateView();
        }

        return Disposable.Create(() =>
        {
            ClearRequestCount--;
            _instance?.UpdateView();
        });
    }

    public static IDisposable ShowGrey()
    {
        GreyRequestCount++;
        if (_instance == null)
        {
            CreateScreen();
        }
        else
        {
            _instance.UpdateView();
        }

        return Disposable.Create(() =>
        {
            GreyRequestCount--;
            _instance?.UpdateView();
        });
    }

    public static IDisposable ShowDark()
    {
        DarkRequestCount++;
        if (_instance == null)
        {
            CreateScreen();
        }
        else
        {
            _instance.UpdateView();
        }

        return Disposable.Create(() =>
        {
            DarkRequestCount--;
            _instance?.UpdateView();
        });
    }


    private static void CreateScreen()
    {
        SceneTree tree = Engine.GetMainLoop() as SceneTree;
        PackedScene loadingScreenScene =
            GD.Load<PackedScene>("res://Application/Scenes/LoadingScreen/LoadingScreen.tscn");
        LoadingScreen loadingScreenInstance = loadingScreenScene.Instantiate<LoadingScreen>();
        loadingScreenInstance.Name = "LoadingScreen";

        // Store reference to the instance
        _instance = loadingScreenInstance;

        // Connect to child_entered_tree signal to monitor new children
        tree.Root.CallDeferred("add_child", _instance);
        tree.Root.ChildEnteredTree += OnChildEnteredTree;
    }

    private static void OnChildEnteredTree(Node child)
    {
        // Only move if we have a loading screen instance and it's not the child being added
        if (_instance != null && child != _instance && _instance.GetParent() != null)
        {
            // Move loading screen to be the last child
            Node parent = _instance.GetParent();
            parent.MoveChild(_instance, parent.GetChildCount() - 1);
        }
    }

    public override void _ExitTree()
    {
        // Clean up the signal connection when the loading screen is removed
        if (Engine.GetMainLoop() is SceneTree tree)
        {
            tree.Root.ChildEnteredTree -= OnChildEnteredTree;
        }

        // Clear the static reference
        if (_instance == this)
        {
            _instance = null;
        }

        base._ExitTree();
    }
}
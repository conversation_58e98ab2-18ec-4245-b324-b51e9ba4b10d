using Godot;
using System.Reactive;
using System.Reactive.Disposables;
using BringMeTreasure;
using BringMeTreasure.Extensions;
using BringMeTreasure.Models;

public partial class Town : Control, ITown
{
    private TownController controller;
    public ITownController Controller => controller ??= new TownController(this);
    [Export] private Button startExpeditionButton;

    public override void _Ready()
    {
        base._Ready();
        Controller.Initialize();
    }

    public IObservable<Unit> OnStartExpeditionButtonPressedAsObservable()
    {
        return startExpeditionButton
            .OnButtonPressedAsObservable();
    }

    public ISave GetSave()
    {
        return GameStateManager.Instance;
    }

    public ISceneManager GetSceneManager()
    {
        return SceneManager.Instance;
    }

    public void AutoSave()
    {
        GameStateManager.Instance.AutoSave();
    }
}

public class TownController(ITown implementation) : ITownController
{
    CompositeDisposable disposables = new();

    public void Initialize()
    {
        ListenForStartExpeditionButton();
    }

    private void ListenForStartExpeditionButton()
    {
        implementation.OnStartExpeditionButtonPressedAsObservable()
            .Subscribe(_ =>
            {
                ISave save = implementation.GetSave();
                save.SetKey(nameof(ISave.Keys.CurrentRoute), nameof(Routes.A));
                ISceneManager sceneManager = implementation.GetSceneManager();
                sceneManager.ChangeScene(ISceneManager.SceneType.Travel);
                implementation.AutoSave();
                GD.Print("Start Expedition Button Pressed");
                // Logic to start the expedition
            })
            .AddTo(disposables);
    }

    public void Dispose()
    {
        disposables?.Dispose();
    }
}

public interface ITownController : IDisposable
{
    void Initialize();
}

public interface ITown
{
    IObservable<Unit> OnStartExpeditionButtonPressedAsObservable();
    ISave             GetSave();
    ISceneManager     GetSceneManager();
    void              AutoSave();
}
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.SimpleStuff;

public class MoneyDisplay : IDisposable
{
    IDisposable job;

    public MoneyDisplay(Label label, IInventory inventory)
    {
        ITranslate   translate             = new Translate();
        int          currentMoney          = inventory.GetMoney();
        Subject<int> kickStartMoneySubject = new Subject<int>();
        job = inventory.OnMoneyChangedAsObservable().Merge(kickStartMoneySubject).Subscribe(m =>
        {
            label.Text = translate.Tr("Money") + ": " + m;
        });
        kickStartMoneySubject.OnNext(currentMoney);
    }

    public void Dispose()
    {
        job?.Dispose();
    }
}
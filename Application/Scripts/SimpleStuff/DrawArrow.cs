using System.Reactive.Disposables;
using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.SimpleStuff;

public partial class DrawArrow : Control, IDrawArrow
{
    private Node2D startObject;
    private Node2D endObject;
    private Color color;
    [Export] private Control arrowAnimating;
    private CompositeDisposable disposables = new();
    ShaderMaterial arrowShader;

    public override void _Ready()
    {
        base._Ready();
        disposables.AddTo(this);
    }

    public void SetPosition(Vector2 start)
    {
        GlobalPosition = start;
    }

    public void SetLength(int length)
    {
        Size = new Vector2(Size.X, length); // Assuming a fixed height for the arrow
    }

    public void PositionArrow(Node2D startObject, Node2D endObject, Color color)
    {
        this.startObject = startObject;
        this.endObject = endObject;
        this.color = color;
        Vector2 start = startObject.GlobalPosition;
        Vector2 end = endObject.GlobalPosition;
        SetArrowPos(end, start);

        SetColor(color);

        GetTree().Root.SizeChanged += OnWindowResized;
        disposables.Add(Disposable.Create(() => GetTree().Root.SizeChanged -= OnWindowResized));
    }

    private void OnWindowResized()
    {
        SetArrowPos(endObject.GlobalPosition, startObject.GlobalPosition);
    }

    private void SetArrowPos(Vector2 end, Vector2 start)
    {
        int hypot = (int) end.DistanceTo(start);
        Vector2 dir = end - start;

// 2) angle in radians
        float angleRad = dir.Angle();

// 3) in degrees
        float angleDeg = Mathf.RadToDeg(angleRad);


        SetPosition(start);
        SetRotationDegrees(angleDeg + 270);
        SetLength(hypot);
    }

    public override void _Notification(int what)
    {
        if (what == MainLoop.NotificationWMAbout)
        {
            SetArrowPos(endObject.GlobalPosition, startObject.GlobalPosition);
        }
    }

    public void Destroy()
    {
        QueueFree();
    }


    public new void Show()
    {
        base.Show();
        SetColor(color);
    }

    private void SetColor(Color color1)
    {
        color = color1;
        arrowShader ??= (ShaderMaterial) arrowAnimating.Material.Duplicate();
        arrowAnimating.Material = arrowShader;
        arrowShader.SetShaderParameter("my_color", color1);
        arrowAnimating.Visible = true;
    }

    public IDisposable Fade(float duration)
    {
        Color transparentColor = new Color(color.R, color.G, color.B, 0);
        
        Tween tween = CreateTween();
        tween.TweenProperty(arrowShader, "shader_parameter/my_color", transparentColor, duration);
        IDisposable d = Disposable.Create(() => { tween.Kill(); });
        disposables.Add(d);
        return d;
    }
}

public interface IDrawArrow
{
    void Hide();
    void Show();
    void Destroy();
    public IDisposable Fade(float duration);
}
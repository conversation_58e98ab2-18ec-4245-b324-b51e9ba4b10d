using Godot;

namespace BringMeTreasure.SimpleStuff;

public class RNG<PERSON>rapper(RandomNumberGenerator rng) : IRng
{
    public float Randf()
    {
        return rng.Randf();
    }

    public float RandfRange(float inclusiveStart, float exclusiveEnd)
    {
        return rng.RandfRange(inclusiveStart, exclusiveEnd);
    }

    public int RandiRange(int inclusiveStart, int exclusiveEnd)
    {
        return rng.RandiRange(inclusiveStart, exclusiveEnd);
    }
}

public interface IRng
{
    public float Randf();
    float        RandfRange(float inclusiveStart, float exclusiveEnd);
    int          RandiRange(int inclusiveStart, int exclusiveEnd);
}
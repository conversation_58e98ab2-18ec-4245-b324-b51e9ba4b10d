using Godot;

namespace BringMeTreasure.SimpleStuff;

[Tool]
[GlobalClass]
public partial class CircleBox : Control
{
    // [Export] 
    private float radius;

    // [Export]
    private float wheel;

    // [Export]
    private float spacing;

    [Export]
    private float Radius
    {
        get => radius;
        set
        {
            radius = value;
            Update(); // Redraw when radius changes
        }
    }

    [Export]
    private float Wheel
    {
        get => wheel;
        set
        {
            wheel = value;
            Update(); // Redraw when wheel changes
        }
    }

    [Export]
    private float Spacing
    {
        get => spacing;
        set
        {
            spacing = value;
            Update(); // Redraw when spacing changes
        }
    }

    public override void _Ready()
    {
        base._Ready();
        Update();
    }

    public void Update()
    {
        List<Control> children = GetChildren().OfType<Control>().ToList();
        Vector2       center   = GlobalPosition + Size / 2;


        for (int i = 0; i < children.Count; i++)
        {
            bool isEven = i % 2 == 0;

            Control child = children[i];

            float angle =
                Mathf.DegToRad(Wheel + spacing * ((i + 1) / 2) * (isEven ? 1 : -1)); // Ensure angle is in degrees


            float   x        = center.X + (radius * Mathf.Cos(angle));
            float   y        = center.Y - (radius * Mathf.Sin(angle));
            Vector2 position = new(x, y);

            child.GlobalPosition = position - child.Size / 2; // Center the child control
        }
    }
}
using Godot;

namespace BringMeTreasure.GodotAsync;

public static class GTask
{
    // public static async Task GetOnMainThread()
    // {
    //     SceneTree tree = Engine.GetMainLoop() as SceneTree;
    //     await tree.Root.ToSignal(tree.Root.GetTree(), SceneTree.SignalName.ProcessFrame);
    // }

    public static async void Forget(this Task task)
    {
        try
        {
            await task;
        }
        catch (OperationCanceledException e)
        {
            // ignore cancellation exceptions
        }
        catch (Exception e)
        {
            GD.PrintErr("Task failed: ", e);
            // Handle other exceptions if necessary
        }
    }
    
    public static async Task WaitUntil(Func<bool> condition)
    {
        while (!condition())
        {
            SceneTree tree = Engine.GetMainLoop() as SceneTree;
            await tree.ToSignal(tree, SceneTree.SignalName.ProcessFrame);
        }
    }
}

using Godot;

namespace BringMeTreasure;

public partial class SceneManager : Node, ISceneManager
{
    public static SceneManager Instance { get; private set; }

    public override void _Ready()
    {
        // enforce singleton
        if (Instance != null && Instance != this)
        {
            QueueFree();
            return;
        }

        Instance = this;
    }

    public void ChangeScene(ISceneManager.SceneType scene)
    {
        string sceneName = $"res://Application/Scenes/{scene}/{scene}.tscn";

        GetTree().ChangeSceneToFile(sceneName);
    }
}

public interface ISceneManager
{
    void ChangeScene(SceneType scene);

    enum SceneType
    {
        Travel,
        Battle,
        Town
    }
}
using BringMeTreasure.SimpleStuff;

namespace BringMeTreasure.Models;

public class Heal(int healAmount, int uses, RegainType regainType) : Talent
{
    private int maxUses = uses;
    int remainingUses = uses;// todo serialize
    public override string GetName()
    {
        return new Translate().Tr("Heal");
    }

    public override string GetDescription()
    {
        return (new Translate().Tr("Heal_desc").Replace("{health}", healAmount.ToString()));
    }

    public override RegainType GetRegainType()
    {
        return regainType;
    }
}
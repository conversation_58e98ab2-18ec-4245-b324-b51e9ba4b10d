using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using Godot;

namespace BringMeTreasure.Extensions;

public static class ReactiveExtensions
{
    public static IObservable<Unit> WhenAll(this IEnumerable<IObservable<bool>> observables)
    {
        return Observable.Create<Unit>(observer =>
        {
            CompositeDisposable disposables = new();
            Dictionary<IObservable<bool>, bool> results = new Dictionary<IObservable<bool>, bool>();

            foreach (var observable in observables)
            {
                results[observable] = false; // Initialize all observables to false
                IDisposable disposable = observable.Subscribe(isTrue =>
                {
                    if(!results.ContainsKey(observable))
                    {
                        return;
                    }
                    results[observable] = isTrue;
                    if (results.Values.Any(b => !b))
                    {
                        return;
                    }

                    //all true
                    observer.OnNext(Unit.Default);
                    observer.OnCompleted();
                }, observer.OnError);

                disposables.Add(disposable);
            }

            return disposables;
        });
    }
    
    
}
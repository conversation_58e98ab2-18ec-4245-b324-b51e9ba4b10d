using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Extensions;

public static class IEnumerableExtensions
{
    public static void ForEach<T>(this IEnumerable<T> list, Action<T> action)
    {
        foreach (T item in list)
        {
            action(item);
        }
    }

    public static T Random<T>(this IEnumerable<T> list)
    {
        if (list == null || !list.Any())
        {
            throw new InvalidOperationException("Cannot select a random element from an empty collection.");
        }


        int index = GD.RandRange(0, list.Count() - 1);
        return list.ElementAt(index);
    }

    public static IEnumerable<T> Shuffle<T>(this IEnumerable<T> source)
    {
        return source.OrderBy(x => Guid.NewGuid());
    }

    public static IEnumerable<T> Shuffle<T>(this IEnumerable<T> source, IRng rng)
    {
        return source.OrderBy(x => rng.RandfRange(0, source.Count()));
    }

    public static T FirstOrNull<T>(this IEnumerable<T> source, Func<T, bool> predicate) where T : class
    {
        foreach (T item in source)
        {
            if (predicate(item))
            {
                return item;
            }
        }

        return null;
    }
}
using System.Reactive.Disposables;
using System.Runtime.CompilerServices;
using Godot;

namespace BringMeTreasure.Extensions;

public static class IDisposableExtensions
{
    public static void AddTo(this IDisposable disposable, CompositeDisposable disposables)
    {
        disposables.Add(disposable);
    }

    private static readonly ConditionalWeakTable<Node, CompositeDisposable> Disposables =
        new();

    public static void AddTo(this IDisposable disposable, Node node)
    {
        if (!Disposables.TryGetValue(node, out var composite))
        {
            composite = new CompositeDisposable();
            Disposables.Add(node, composite);

            // Dispose when node exits the scene tree
            node.TreeExiting += () =>
            {
                composite.Dispose();
                Disposables.Remove(node);
            };
        }

        composite.Add(disposable);
    }
}
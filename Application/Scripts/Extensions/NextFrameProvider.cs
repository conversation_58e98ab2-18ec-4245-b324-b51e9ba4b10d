using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using Godot;

namespace BringMeTreasure.Extensions;

public class NextFrameProvider : IFrameProvider
{
    public IObservable<Unit> OnNextFrameAsObservable()
    {
        SceneTree tree = Engine.GetMainLoop() as SceneTree;
        // add a basic node to the scene tree that will emit a signal every frame
        Node node = new Node();
        tree.Root.AddChild(node);

        IObservable<Unit> frameProvider = Observable
            .FromEvent(
                handler => tree.ProcessFrame += handler,
                handler => tree.ProcessFrame -= handler
            ).ObserveOn(SynchronizationContext.Current!);

        return Observable.Create<Unit>(observer =>
        {
            IDisposable subscription = frameProvider.Subscribe(observer);
            return Disposable.Create(() =>
            {
                // Remove the node from the scene tree to stop receiving frames
                if (node.GetParent() != null)
                {
                    node.GetParent().RemoveChild(node);
                }

                node.QueueFree(); // Free the node to avoid memory leaks
                subscription.Dispose();
            });
        });
    }
}

public static class NextFrameProviderExtensions
{
    public static IObservable<T> SkipFrame<T>(this IObservable<T> observable, int frames, IFrameProvider frameProvider)
    {
        Subject<T> subject = new Subject<T>();
        IDisposable d = observable.Subscribe(t =>
        {
            frameProvider.OnNextFrameAsObservable()
                .Skip(frames - 1)
                .Take(1)
                .Subscribe(_ => subject.OnNext(t));
        }, subject.OnError, subject.OnCompleted);

        return Observable.Create<T>(observer =>
        {
            IDisposable subscription = subject.Subscribe(observer);
            return Disposable.Create(() =>
            {
                d.Dispose();
                subscription.Dispose();
            });
        });
    }
}

public interface IFrameProvider
{
    IObservable<Unit> OnNextFrameAsObservable();
}
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using Godot;

namespace BringMeTreasure.Extensions;

public static class ButtonExtensions
{
    public static IObservable<Unit> OnButtonPressedAsObservable(this BaseButton button)
    {
        return Observable.Create<Unit>(observer =>
        {
            void OnPressed()
            {
                observer.OnNext(Unit.Default);
            }

            button.Pressed += OnPressed;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { button.Pressed -= OnPressed; });
        });
    }

    public static IObservable<Unit> OnButtonDownAsObservable(this BaseButton button)
    {
        return Observable.Create<Unit>(observer =>
        {
            void OnDown()
            {
                observer.OnNext(Unit.Default);
            }

            button.ButtonDown += OnDown;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { button.ButtonDown -= OnDown; });
        });
    }

    public static IObservable<Unit> OnMouseEnterAsObservable(this Control c)
    {
        return Observable.Create<Unit>(observer =>
        {
            void OnEnter()
            {
                observer.OnNext(Unit.Default);
            }

            c.MouseEntered += OnEnter;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { c.MouseEntered -= OnEnter; });
        });
    }

    public static IObservable<Unit> OnMouseExitAsObservable(this Control c)
    {
        return Observable.Create<Unit>(observer =>
        {
            void OnExit()
            {
                observer.OnNext(Unit.Default);
            }

            c.MouseExited += OnExit;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { c.MouseExited -= OnExit; });
        });
    }

    public static IObservable<Unit> OnButtonReleasedAsObservable(this BaseButton button)
    {
        return Observable.Create<Unit>(observer =>
        {
            void OnReleased()
            {
                observer.OnNext(Unit.Default);
            }

            button.ButtonUp += OnReleased;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { button.ButtonUp -= OnReleased; });
        });
    }

    public static IObservable<bool> OnToggledAsObservable(this BaseButton button)
    {
        if (!button.IsToggleMode())
        {
            throw new ArgumentException($"button by name of \"{button.Name}\" is not set to be a toggle button.");
        }

        return Observable.Create<bool>(observer =>
        {
            void OnToggled(bool buttonPressed)
            {
                observer.OnNext(buttonPressed);
            }

            button.Toggled += OnToggled;

            // Return a disposable that removes the event handler
            return Disposable.Create(() => { button.Toggled -= OnToggled; });
        });
    }
}
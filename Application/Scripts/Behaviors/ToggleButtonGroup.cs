using System.Reactive;
using System.Reactive.Disposables;
using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.Behaviors;

public class ToggleButtonGroup : IToggleButtonGroup
{
    private readonly CompositeDisposable disposables = new();

    private readonly IToggleButton[] btns;

    public ToggleButtonGroup(IEnumerable<IToggleButton> buttons, IObservable<Unit> additionalUntoggleSource = null)
    {
        btns = buttons.ToArray();
        buttons.ForEach(b => b.SetToggleMode(true));
        buttons.ForEach(b => b.OnToggledAsObservable()
            .Subscribe(isOn =>
            {
                if (!isOn)
                {
                    b.OnUntoggled();
                    return;
                }

                buttons.Except([b]).ForEach(otherButton =>
                {
                    if (otherButton.IsPressed())
                    {
                        otherButton.SetDisabled(false);
                        otherButton.SetPressedNoSignal(false);
                        otherButton.OnUntoggled();
                    }
                });

                b.OnToggled();
                b.SetDisabled(true);
            }).AddTo(disposables));

        additionalUntoggleSource?.Subscribe(_ =>
        {
            buttons.ForEach(otherButton =>
            {
                if (otherButton.IsPressed())
                {
                    otherButton.SetPressedNoSignal(false);
                    otherButton.OnUntoggled();
                }
            });
        }).AddTo(disposables);
    }

    public void Dispose()
    {
        disposables.Dispose();
    }

    public void ResendPressedSignal()
    {
        btns.Where(b => b.IsPressed()).ForEach(b =>
        {
            b.OnUntoggled();
            b.OnToggled();
        });
    }
}

public interface IToggleButtonGroup : IDisposable
{
}

public interface IToggleButton
{
    void SetPressedNoSignal(bool pressed);
    bool IsPressed();
    void SetToggleMode(bool enabled);
    void OnUntoggled();
    void OnToggled();
    IObservable<bool> OnToggledAsObservable();
    void SetDisabled(bool b);
}
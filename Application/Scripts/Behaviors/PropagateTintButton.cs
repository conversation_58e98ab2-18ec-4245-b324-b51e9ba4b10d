using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.Behaviors;

[GlobalClass]
public partial class PropagateTintButton : TextureButton, IPropagateTint
{
    [Export] Color modulateColor = Colors.White;

    public override void _Ready()
    {
        base._Ready();
        Color defaultColor = Modulate;
        this.OnButtonDownAsObservable().Subscribe(_ => Modulate = modulateColor)
            .AddTo(this);
        this.OnButtonReleasedAsObservable()
            .Subscribe(_ => Modulate = defaultColor)
            .AddTo(this);
    }
}
using System.Reactive.Disposables;
using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.Behaviors;

public class PropagateTint : IPropagateTint
{
    CompositeDisposable disposables = new();

    public PropagateTint(BaseButton button)
    {
        StyleBoxTexture sb = button.GetThemeStylebox("pressed", "Button") as StyleBoxTexture;
        Color pressedColor = sb?.ModulateColor ?? Colors.White;
        Color originalColor = button.Modulate;

        button.OnButtonDownAsObservable()
            .Subscribe(_ => { button.Modulate = pressedColor; }).AddTo(disposables);
        button.OnButtonReleasedAsObservable()
            .Subscribe(_ => { button.Modulate = originalColor; }).AddTo(disposables);
    }
    
    public PropagateTint(BaseButton button, Color pressedColor)
    {
        Color originalColor = button.Modulate;

        button.OnButtonDownAsObservable()
            .Subscribe(_ => { button.Modulate = pressedColor; }).AddTo(disposables);
        button.OnButtonReleasedAsObservable()
            .Subscribe(_ => { button.Modulate = originalColor; }).AddTo(disposables);
    }

    public void Dispose()
    {
        disposables?.Dispose();
    }
}



public interface IPropagateTint : IDisposable
{
}
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using BringMeTreasure.Extensions;
using Godot;

namespace BringMeTreasure.Behaviors;

public class Tab(BaseButton button) : ITab
{
    private Subject<Unit> onTabSelectedSubject = new();

    public void SetPressedNoSignal(bool pressed)
    {
        button.SetPressedNoSignal(pressed);
    }

    public bool IsPressed()
    {
        return button.IsPressed();
    }

    public void SetToggleMode(bool enabled)
    {
        button.SetToggleMode(enabled);
    }

    public void OnUntoggled()
    {
    }

    public void OnToggled()
    {
        onTabSelectedSubject.OnNext(Unit.Default);
    }

    public IObservable<bool> OnToggledAsObservable()
    {
        return button.OnToggledAsObservable();
    }

    public void SetDisabled(bool b)
    {
        button.SetDisabled(b);
    }

    public void SetTabName(string name)
    {
        if (button is Button b)
        {
            b.Text = name;
        }
    }

    public IObservable<Unit> OnTabSelectedAsObservable()
    {
        return onTabSelectedSubject.AsObservable();
    }

    public void SetVisible(bool b)
    {
        button.Visible = b;
    }

    public void SetPressed(bool b)
    {
        button.SetPressed(b);
    }
}

public interface ITab : IToggleButton
{
    void SetTabName(string name);
    IObservable<Unit> OnTabSelectedAsObservable();
    void SetVisible(bool b);
    void SetPressed(bool b);
}
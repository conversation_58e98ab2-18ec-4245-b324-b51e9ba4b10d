using Godot;

namespace BringMeTreasure.Behaviors;

public static class Projectile
{
    public static async Task ParabolicLaunch(Node2D startNode, Node2D endNode, Node2D projectileNode, float height, float duration,
        float peakT = 0.5f)
    {
        Vector2 start = startNode.GlobalPosition;
        Vector2 end = endNode.GlobalPosition;

        float t = 0f;
        float time = 0f;

        while (t < 1f)
        {
            await projectileNode.ToSignal(projectileNode.GetTree(), SceneTree.SignalName.ProcessFrame);

            time += (float) projectileNode.GetProcessDeltaTime();
            t = time / duration; // goes from 0 to 1
            Vector2 position = CalculateParabolicPosition(start, end, height, t);
            projectileNode.GlobalPosition = position;
        }
    }
    private static Vector2 CalculateParabolicPosition(Vector2 origin, Vector2 target, float peakHeight, float t)
    {
        // Lerp horizontally
        float x = Mathf.Lerp(origin.X, target.X, t);

        // Peak is highest Y point (Godot Y increases downward)
        float peakY = Mathf.Min(origin.Y, target.Y) - peakHeight;

        // Three-point parabolic interpolation (origin → peak → target)
        float yA = origin.Y;
        float yB = peakY;
        float yC = target.Y;

        float oneMinusT = 1f - t;
        float y = oneMinusT * oneMinusT * yA + 2f * oneMinusT * t * yB + t * t * yC;

        return new Vector2(x, y);
    }
    
    public static async Task DirectLaunch(Node2D startNode, Node2D endNode, Node2D projectileNode, float duration)
    {
        Vector2 start = startNode.GlobalPosition;
        Vector2 end = endNode.GlobalPosition;

        float t = 0f;
        while (t < 1f)
        {
            await projectileNode.ToSignal(projectileNode.GetTree(), SceneTree.SignalName.ProcessFrame);
            t += (float) projectileNode.GetProcessDeltaTime() / duration;
            projectileNode.GlobalPosition = start.Lerp(end, t);
        }
    }
}
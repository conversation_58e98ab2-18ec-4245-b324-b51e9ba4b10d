using Godot;

namespace BringMeTreasure.SimpleStuff;

public class CenterOnControl
{
    public CenterOnControl(Node2D toCenter, Control thingToCenterIn)
    {
        if (toCenter == null || thingToCenterIn == null)
        {
            return;
        }

        // Center the toCenter node in the thingToCenterIn control
        Vector2 centerPosition = thingToCenterIn.GetGlobalRect().Position + thingToCenterIn.GetGlobalRect().Size / 2;
        toCenter.GlobalPosition = centerPosition;
        
    }
}

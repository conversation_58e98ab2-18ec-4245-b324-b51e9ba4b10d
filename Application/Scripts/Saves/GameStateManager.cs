using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;
using Godot;

namespace BringMeTreasure;

public partial class GameStateManager : Node, ISave
{
    // the singleton instance
    public static GameStateManager Instance { get; private set; }

    // your actual state object
    private GameState State { get; set; }

    private ISaveProvider saveProvider;

    public override void _Ready()
    {
        // enforce singleton
        if (Instance != null && Instance != this)
        {
            QueueFree();
            return;
        }

        Instance = this;

        saveProvider = new GodotSaveProvider();
        // State = new GameState();
        LoadMostRecentSave();
    }

    public void LoadMostRecentSave()
    {
        List<string> saves = saveProvider.GetSaveList();
        if (saves.Count == 0)
        {
            GD.Print("No saves found.");
            State = new GameState();
            return;
        }

        // Load the most recent save
        string mostRecentSave = saveProvider.GetMostRecentOrEmpty();
        State = saveProvider.GetSave(mostRecentSave);
    }

    public T GetKey<T>(string key)
    {
        return State.GetKey<T>(key);
    }

    public void SetKey<T>(string key, T value)
    {
        State.SetKey(key, value);
    }

    public bool HasKey(string key)
    {
        return State.HasKey(key);
    }

    public ICharacterData[] GetParty()
    {
        return State.GetParty();
    }

    public void SetParty(ICharacterData[] p)
    {
        State.SetParty(p);
    }

    public IInventory GetInventory()
    {
        return State.GetInventory();
    }

    public ISupply GetSupply()
    {
        return State.GetSupply();
    }

    public void RemoveKey(string key)
    {
        State.RemoveKey(key);
    }

    public void SetInventory(IInventory i)
    {
        State.SetInventory(i);
    }

    public void AutoSave()
    {
        saveProvider.AutoSave(State);
    }
}
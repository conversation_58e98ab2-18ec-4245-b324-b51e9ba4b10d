using BringMeTreasure.Models;
using BringMeTreasure.Scripts.Characters;
using Newtonsoft.Json;

namespace BringMeTreasure;

[JsonObject(MemberSerialization.Fields)]
public class GameState : ISave
{
    [JsonIgnore] public const int CurrentVersion = 0;

    [JsonProperty] public int Version = CurrentVersion;

    private Dictionary<string, string> data = new();


    private CharacterData[] party = [];


    private Inventory inventory = new();
    private Supply supply = new();

    public GameState UpdateFrom(int gameStateVersion)
    {
        return this;
    }

    public T GetKey<T>(string key)
    {
        return JsonConvert.DeserializeObject<T>(data[key]);
    }

    public void SetKey<T>(string key, T value)
    {
        data[key] = JsonConvert.SerializeObject(value);
    }

    public bool HasKey(string key)
    {
        return data.ContainsKey(key);
    }

    public ICharacterData[] GetParty()
    {
        return party.ToArray();
    }

    public void SetParty(ICharacterData[] p)
    {
        party = p.Select(c => (CharacterData) c).ToArray();
    }

    public IInventory GetInventory()
    {
        if (inventory == null)
        {
            inventory = new Inventory();
        }
        return inventory;
    }

    public ISupply GetSupply()
    {
        if (supply == null)
        {
            supply = new Supply();
        }
        return supply;
    }

    public void RemoveKey(string key)
    {
        data.Remove(key);
    }

    public void SetInventory(IInventory i)
    {
        inventory = (Inventory) i;
    }

    public static JsonSerializerSettings GetJsonSettings()
    {
        var settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.Auto,
            SerializationBinder = new NamespaceFilteringBinder("BringMeTreasure")
        };
        return settings;
    }
}

public interface ISave
{
    T                GetKey<T>(string key);
    void             SetKey<T>(string key, T value);
    bool             HasKey(string key);
    ICharacterData[] GetParty();
    void             SetParty(ICharacterData[] p);
    IInventory       GetInventory();
    ISupply          GetSupply();

    enum Keys
    {
        CurrentRoute,
        RngSeed,
        RngState,
        StopIndex,
        Arrived
    }

    void RemoveKey(string key);
}
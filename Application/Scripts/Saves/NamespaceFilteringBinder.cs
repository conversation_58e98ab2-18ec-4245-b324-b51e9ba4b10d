using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace BringMeTreasure;

public class NamespaceFilteringBinder : ISerializationBinder
{
    private readonly string allowedNamespace;

    public NamespaceFilteringBinder(string allowedNamespace)
    {
        this.allowedNamespace = allowedNamespace;
    }

    public Type BindToType(string assemblyName, string typeName)
    {
        Type type = Type.GetType(typeName);

        if (type == null)
            throw new JsonSerializationException($"Type not found: {typeName}");

        if (!type.Namespace?.StartsWith(allowedNamespace) ?? true)
            throw new JsonSerializationException($"Type '{type.FullName}' is not allowed.");

        return type;
    }

    public void BindToName(Type serializedType, out string assemblyName, out string typeName)
    {
        assemblyName = serializedType.Assembly.FullName;
        typeName = serializedType.FullName;
    }
}
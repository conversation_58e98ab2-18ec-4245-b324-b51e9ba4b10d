using System.Globalization;
using System.Text;
using Newtonsoft.Json;

namespace BringMeTreasure;

public class GodotSaveProvider : ISaveProvider
{
    const string SavePath = "user://saves/";
    public const string DateTimeFormat = "yyyyMMddHHmmss";

    public void AutoSave(GameState gs)
    {
        List<string> saves = GetSaveList();
        List<string> autoSaves = saves.Where(s => s.StartsWith("autosave_")).ToList();
        if (autoSaves.Count >= 5)
        {
            // Remove the oldest autosave
            string oldestSave = autoSaves
                .OrderBy(s =>
                    DateTime.ParseExact(s.Replace("autosave_", ""), DateTimeFormat, CultureInfo.InvariantCulture))
                .First();
            File.Delete(SavePath + oldestSave + ".json");
        }

        string autoSaveName = "autosave";
        SetSave(gs, autoSaveName);
    }

    public List<string> GetSaveList()
    {
        if (!Directory.Exists(SavePath))
        {
            Directory.CreateDirectory(SavePath);
        }

        List<string> saves = new();
        var files = Directory.GetFiles(SavePath, "*.json");
        foreach (var file in files)
        {
            string fileName = Path.GetFileNameWithoutExtension(file);
            saves.Add(fileName);
        }

        return saves;
    }

    public string GetMostRecentOrEmpty()
    {
        List<string> saves = GetSaveList();
        if (saves.Count == 0)
        {
            return String.Empty;
        }

        string newest = saves
            .OrderByDescending(s =>
                DateTime.ParseExact(s.Replace(".json", "").Split("_").Last(), DateTimeFormat, CultureInfo.InvariantCulture)).First();
        return newest;
    }

    public GameState GetSave(string name)
    {
        string filePath = SavePath + name + ".json";
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException("Save file not found: " + filePath);
        }

        using var f = File.OpenRead(filePath);
        using var reader = new StreamReader(f, Encoding.UTF8);
        string json = reader.ReadToEnd();

        GameState gameState = JsonConvert.DeserializeObject<GameState>(json, GameState.GetJsonSettings());
        if (gameState == null)
        {
            return new GameState(); // todo this is gitchy
        }

        while (gameState.Version < GameState.CurrentVersion)
        {
            gameState = gameState.UpdateFrom(gameState.Version);
        }

        return gameState;
    }

    public void SetSave(GameState gs, string name)
    {
        using var f =
            File.Open($"{SavePath}{name}_{DateTime.Now.ToString(DateTimeFormat, CultureInfo.InvariantCulture)}.json",
                FileMode.Create);
        using var writer = new StreamWriter(f, Encoding.UTF8);
        string json = JsonConvert.SerializeObject(gs, Formatting.Indented, GameState.GetJsonSettings());
        writer.Write(json);
    }
}

public interface ISaveProvider
{
    GameState GetSave(string name);
    void SetSave(GameState gs, string name);
    void AutoSave(GameState gs);
    public List<string> GetSaveList();
    string GetMostRecentOrEmpty();
}
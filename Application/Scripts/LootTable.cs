using BringMeTreasure.Extensions;
using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Models;

[GlobalClass]
public partial class LootTable : Resource, ILootTable
{
    [Export] private Godot.Collections.Array<LootTableRow> items = new();

    private LootTableRowInstance[] GetItems()
    {
        if (items == null || items.Count == 0)
        {
            GD.PrintErr("LootTable is empty or not initialized.");
            return [];
        }

        return items.Select(item => item.Get()).ToArray();
    }

    public SupplyItemType[] Roll(IRng rng, int numberOfRolls)
    {
        var items = GetItems();
        return StaticRoll(items, rng, numberOfRolls);
    }


    public class LootTableRowInstance
    {
        public SupplyItemType Item;
        public System.Numerics.Vector2 QuantityRange;
        public Rarity Rarity;

        public LootTableRowInstance(SupplyItemType item, System.Numerics.Vector2 quantity, Rarity rarity)
        {
            Item          = item;
            QuantityRange = quantity;
            Rarity        = rarity;
        }
    }

    public static SupplyItemType[] StaticRoll(LootTableRowInstance[] items, IRng rng, int numberOfRolls)
    {
        if (items == null || items.Length == 0)
        {
            return [];
        }

        List<SupplyItemType> rolledItems = new List<SupplyItemType>();

        for (int i = 0; i < numberOfRolls; i++)
        {
            Rarity rarity = Rarity.None;
            float  roll   = rng.Randf();

            switch (roll)
            {
                case < 0.3f:
                    rarity = Rarity.None;
                    break;
                case < 0.5f:
                    rarity = Rarity.Common;
                    break;
                case < 0.65f:
                    rarity = Rarity.Uncommon;
                    break;
                case < 0.80f:
                    rarity = Rarity.Rare;
                    break;
                case < 0.95f:
                    rarity = Rarity.VeryRare;
                    break;
                case <= 1f:
                    rarity = Rarity.Legendary;
                    break;
            }

            LootTableRowInstance[] matchedRarityItems = items.Where(item => item.Rarity == rarity).ToArray();
            while (matchedRarityItems.Length == 0)
            {
                if (rarity == Rarity.None)
                {
                    break; // Skip if no item is rolled
                }

                rarity--;
                matchedRarityItems = items.Where(item => item.Rarity == rarity).ToArray();
            }

            if (rarity == Rarity.None)
            {
                continue; // Skip if no item is rolled
            }

            LootTableRowInstance hitItem = matchedRarityItems.Shuffle(rng).First();
            int quantity = rng.RandiRange((int) hitItem.QuantityRange.X, (int) hitItem.QuantityRange.Y + 1);
            rolledItems.AddRange(Enumerable.Repeat(hitItem.Item, quantity));
        }

        return rolledItems.ToArray();
    }
}



public enum Rarity
{
    None = 0,
    Common = 1,
    Uncommon = 2,
    Rare = 3,
    VeryRare = 4,
    Legendary = 5
}

public interface ILootTable
{
    public SupplyItemType[] Roll(IRng rng, int numberOfRolls);
}
using BringMeTreasure.Models;

namespace BringMeTreasure.Abilities;

[Serializable]
public class SwordAttack : CombatAbility
{
    [Newtonsoft.Json.JsonProperty]
    private int damage;

    public SwordAttack(string name, string description, int uses, int damage)
    {
        this.damage = damage;
        this.uses = uses;
        this.name = name;
        this.description = description;
    }
    public override ICombatAbilityEffect[] GetEffects()
    {
        return
        [
            new DamageEffect(damage),
            new DamageEffect(damage / 2)
        ];
    }

    public override RegainType GetRegainType()
    {
        return RegainType.None;
    }
}
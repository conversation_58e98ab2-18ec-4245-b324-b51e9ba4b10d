using BringMeTreasure.Models;

namespace BringMeTreasure.Abilities;

[Serializable]
public class Fortify : CombatAbility
{
    [Newtonsoft.Json.JsonProperty]
    public int armorAmount;

    public Fortify(string name, string description, int uses, int armorAmount)
    {
        this.armorAmount = armorAmount;
        this.uses = uses;
        this.name = name;
        this.description = description;
    }

    public override ICombatAbilityEffect[] GetEffects()
    {
        return
        [
            new ArmorBoostEffect(armorAmount)
        ];
    }

    public override RegainType GetRegainType()
    {
        return RegainType.CombatEnd;
    }
}
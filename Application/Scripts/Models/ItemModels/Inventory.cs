using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Runtime.Serialization;
using BringMeTreasure.Application.Items.Relics;
using Godot;
using Newtonsoft.Json;

namespace BringMeTreasure.Models;

[JsonObject(MemberSerialization.Fields)]
public class Inventory : IInventory
{
    private List<IDisplayItem> items = new();

    private int money = 0;
    [JsonIgnore] private Subject<int> moneyChangedSubject = new();

    public Inventory()
    {
        items.Add(new CraftedItem("Test Item", "This is a test item",
            "res://Application/Items/4x/armor and clothing/blue dress 4x.png", ItemType.Weapon,
            [new BasicAttackRelic()]));
    }

    public void AddItem(IDisplayItem craftedItem)
    {
        items.Add(craftedItem);
    }

    public List<IDisplayItem> GetItems()
    {
        return items;
    }

    public List<IStatProviderItem> GetResourceItems()
    {
        return items.OfType<IStatProviderItem>().ToList();
    }

    public List<IAbilityProviderItem> GetCraftedItems()
    {
        return items.OfType<IAbilityProviderItem>().ToList();
    }

    public void RemoveItem(IAbilityProviderItem item)
    {
        if (items.Contains(item))
        {
            items.Remove(item);
        }
    }

    public int GetMoney()
    {
        return money;
    }

    public void AddMoney(int amount)
    {
        money += amount;
        moneyChangedSubject.OnNext(money);
    }

    public bool CanAfford(int cost)
    {
        return money >= cost;
    }

    public void SpendMoney(int cost)
    {
        money -= cost;
        moneyChangedSubject.OnNext(money);
    }

    public IObservable<int> OnMoneyChangedAsObservable()
    {
        return moneyChangedSubject.AsObservable();
    }

    [OnDeserialized]
    internal void OnDeserializedMethod(StreamingContext context)
    {
        moneyChangedSubject = new();
    }
}

public interface IInventory
{
    void                       AddItem(IDisplayItem craftedItem);
    List<IDisplayItem>         GetItems();
    List<IStatProviderItem>    GetResourceItems();
    List<IAbilityProviderItem> GetCraftedItems();
    void                       RemoveItem(IAbilityProviderItem item);
    int                        GetMoney();
    void                       AddMoney(int amount);
    bool                       CanAfford(int cost);
    void                       SpendMoney(int cost);
    IObservable<int>           OnMoneyChangedAsObservable();
}
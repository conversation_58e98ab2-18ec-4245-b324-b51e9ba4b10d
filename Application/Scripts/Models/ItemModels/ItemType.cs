namespace BringMeTreasure.Models;

public enum ItemType
{
    Resource,
    Food,
    Helmet,
    <PERSON>race<PERSON>,
    <PERSON><PERSON>,
    <PERSON>nic,
    BackPack,
    Weapon,
    QuickItem
}

public enum SupplyItemType
{
    Wood,
    OldBones,
    SlimeLining
}
public enum SupplyItemCategory
{
    Binder,
    Structure,
    Essence
}


public interface IDisplayItem
{
    public string GetItemName();
    public string GetItemDescription();
    public string GetItemIconPath();
    public ItemType GetItemType();
}

public interface IStatProviderItem: IDisplayItem
{
    public EffectAmount[] GetResistances();
    public EffectAmount[] GetEffects();
    public SkillBonus[] GetSkillBonuses();
}

public interface IAbilityProviderItem: IStatProviderItem
{
    public ICombatAbility[] GetCombatAbilities();
    public ITalent[] GetTalents();
}
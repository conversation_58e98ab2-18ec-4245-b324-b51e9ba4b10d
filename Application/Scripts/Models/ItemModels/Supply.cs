using Godot;
using Newtonsoft.Json;

namespace BringMeTreasure.Models;

[JsonObject(MemberSerialization.Fields)]
public class Supply : ISupply
{
    Dictionary<SupplyItemType, int> supplyItems = new();
    [JsonIgnore] private List<SupplyItem> resources = new();

    public void AddSupplyItem(SupplyItemType item, int amount)
    {
        if (supplyItems.ContainsKey(item))
        {
            supplyItems[item] += amount;
        }
        else
        {
            supplyItems[item] = amount;
        }
    }

    public void RemoveSupplyItem(SupplyItemType item, int amount)
    {
        if (supplyItems.ContainsKey(item))
        {
            supplyItems[item] -= amount;
            if (supplyItems[item] <= 0)
            {
                supplyItems.Remove(item);
            }
        }
        else
        {
            throw new KeyNotFoundException("Item not found in supply.");
        }
    }

    public Dictionary<IDisplayItem, int> GetSupplyItems()
    {
        if (resources == null || resources.Count == 0)
        {
            string[] files = Directory.GetFiles("res://Application/Items/SupplyItems");
            resources = new List<SupplyItem>();
            foreach (string file in files)
            {
                if (file.EndsWith(".tres"))
                {
                    var resource = GD.Load<SupplyItemResource>(file);
                    if (resource != null)
                    {
                        resources.Add(resource.GetInstance());
                    }
                }
            }
        }


        return supplyItems.ToDictionary(kvp => (IDisplayItem) resources.First(r => r.GetSupplyItemType() == kvp.Key),
            kvp => kvp.Value);
    }
}

public interface ISupply
{
    void                          AddSupplyItem(SupplyItemType item, int amount);
    void                          RemoveSupplyItem(SupplyItemType item, int amount);
    Dictionary<IDisplayItem, int> GetSupplyItems();
}
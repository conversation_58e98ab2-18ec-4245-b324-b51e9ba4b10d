using Godot;

namespace BringMeTreasure.Models;

[GlobalClass]
public partial class SupplyItemResource : Resource
{
    [Export] private SupplyItemType supplyItemType;
    [Export] private Godot.Collections.Dictionary<EffectType, int> possibleResistances = new();
    [Export] private Godot.Collections.Dictionary<EffectType, int> possibleEffects = new();
    [Export] private Godot.Collections.Dictionary<SkillType, int> skillBonuses = new();
    [Export] private string name;
    [Export] private string description;
    [Export] private Texture2D icon;
    [Export] private int sellValue;


    public SupplyItem GetInstance()
    {
        return new SupplyItem(
            name,
            description,
            icon,
            sellValue,
            supplyItemType,
            possibleResistances.Select(kvp => new EffectAmount(kvp.Key, kvp.Value)).ToList(),
            possibleEffects.Select(kvp => new EffectAmount(kvp.Key,     kvp.Value)).ToList(),
            skillBonuses.Select(kvp => new SkillBonus(kvp.Key, kvp.Value)).ToList()
        );
    }

    public static SupplyItem LoadItem(SupplyItemType itemType)
    {
        string path     = $"res://Application/Items/SupplyItems/{itemType}.tres";
        var    resource = GD.Load<SupplyItemResource>(path);
        if (resource == null)
        {
            GD.PrintErr($"Failed to load supply item resource from path: {path}");
            return null;
        }

        return resource.GetInstance();
    }
}

public class SupplyItem : ISupplyItem
{
    private readonly SupplyItemType supplyItemType;
    private readonly string itemName;
    private readonly string itemDescription;
    private readonly Texture2D icon;
    private readonly int sellValue;
    private readonly List<EffectAmount> resistances;
    private readonly List<SkillBonus> skillBonuses;
    private readonly List<EffectAmount> effects;

    public SupplyItem(string itemName,
        string itemDescription,
        Texture2D icon,
        int sellValue,
        SupplyItemType supplyItemType,
        List<EffectAmount> resistances,
        List<EffectAmount> effects,
        List<SkillBonus> skillBonuses)
    {
        this.supplyItemType  = supplyItemType;
        this.resistances     = resistances;
        this.skillBonuses    = skillBonuses;
        this.effects         = effects;
        this.itemName        = itemName;
        this.itemDescription = itemDescription;
        this.icon            = icon;
        this.sellValue       = sellValue;
    }


    public string GetItemName()
    {
        return itemName;
    }

    public string GetItemDescription()
    {
        return itemDescription;
    }

    public string GetItemIconPath()
    {
        return icon.ResourcePath;
    }

    public Texture2D GetIcon()
    {
        return icon;
    }

    public ItemType GetItemType()
    {
        return ItemType.Resource;
    }

    public EffectAmount[] GetResistances()
    {
        return resistances.ToArray();
    }

    public SkillBonus[] GetSkillBonuses()
    {
        return skillBonuses.ToArray();
    }

    public SupplyItemType GetSupplyItemType()
    {
        return supplyItemType;
    }

    public EffectAmount[] GetEffects()
    {
        return effects.ToArray();
    }
}

public interface ISupplyItem : IStatProviderItem
{
    public SupplyItemType GetSupplyItemType();
    public Texture2D GetIcon();
}
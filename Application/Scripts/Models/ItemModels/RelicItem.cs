using Godot;

namespace BringMeTreasure.Models;

public abstract partial class RelicItem : Resource, IAbilityProviderItem
{
    public abstract string GetItemName();
    public abstract string GetItemDescription();
    public abstract string GetItemIconPath();
    public abstract ItemType GetItemType();
    public abstract EffectAmount[] GetResistances();
    public abstract EffectAmount[] GetEffects();
    public abstract SkillBonus[] GetSkillBonuses();
    public abstract ICombatAbility[] GetCombatAbilities();
    public abstract ITalent[] GetTalents();
}
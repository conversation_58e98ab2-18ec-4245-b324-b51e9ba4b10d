using Newtonsoft.Json;

namespace BringMeTreasure.Models;

[JsonObject(MemberSerialization.Fields)]
public class CraftedItem : IAbilityProviderItem
{
    private string itemName;
    private string itemDescription;
    private string itemIconPath;
    private ItemType itemType;
    private List<IStatProviderItem> components = new();


    public CraftedItem(string itemName, string itemDescription, string itemIconPath, ItemType itemType,
        IStatProviderItem[] components)
    {
        this.itemName = itemName;
        this.itemDescription = itemDescription;
        this.itemIconPath = itemIconPath;
        this.itemType = itemType;
        this.components = components.ToList();
    }

    public string GetItemName()
    {
        return itemName;
    }

    public string GetItemDescription()
    {
        return "make and item description";
    }

    public string GetItemIconPath()
    {
        return itemIconPath;
    }

    public ItemType GetItemType()
    {
        return itemType;
    }

    public EffectAmount[] GetResistances()
    {
        return components
            .SelectMany(component => component.GetResistances())
            .ToArray();
    }

    public EffectAmount[] GetEffects()
    {
        return components
            .SelectMany(component => component.GetEffects())
            .ToArray();
    }

    public SkillBonus[] GetSkillBonuses()
    {
        return components
            .SelectMany(component => component.GetSkillBonuses())
            .ToArray();
    }

    public ICombatAbility[] GetCombatAbilities()
    {
        return components
            .OfType<IAbilityProviderItem>()
            .SelectMany(component => component.GetCombatAbilities())
            .ToArray();
    }

    public ITalent[] GetTalents()
    {
        return components
            .OfType<IAbilityProviderItem>()
            .SelectMany(component => component.GetTalents())
            .ToArray();
    }
}
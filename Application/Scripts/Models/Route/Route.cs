using BringMeTreasure.SimpleStuff;
using Godot;

namespace BringMeTreasure.Models;

// a camp provides a full heal and refresh of all status effects
// it also provides 10 hours of resource gather, crafting, and hunting
[GlobalClass]
public partial class Route : Resource, IRoute
{
    // at a destination, you can have as many hours as you want for camp activities. Diminishing returns
    [Export] Godot.Collections.Array<Routes> possibleDestinations = new();

    [Export]
    Godot.Collections.Array<Stop> stopsAlongTheRoute = new(); // after evert stop each character can use on talent

    Godot.Collections.Array<DestinationActivities>
        destinationActivities = new(); // after evert stop each character can use on talent

    [Export] Godot.Collections.Dictionary<SupplyItemType, float> itemAndChance = new();
    [Export] Godot.Collections.Dictionary<RelicItem, float> relicChances = new();
    [Export] private string routeDescription;

    [Export] string routeNameKey;
    [Export] private Texture2D routeImage;
    [Export] private int numberCamps;
    [Export] private Routes routeType;
    [Export] private Vector2I stopRange;

    static ITranslate translate = new Translate();

    public Routes[] GetPossibleDestinations()
    {
        if (possibleDestinations == null || possibleDestinations.Count == 0)
        {
            GD.PrintErr("No possible destinations set for the route.");
            return [];
        }

        return possibleDestinations.ToArray();
    }

    public IStop[] GetStopsAlongTheRoute()
    {
        if (stopsAlongTheRoute == null || stopsAlongTheRoute.Count == 0)
        {
            return [];
        }

        return stopsAlongTheRoute.ToArray<IStop>();
    }

    public DestinationActivities[] GetDestinationActivities()
    {
        if (destinationActivities == null || destinationActivities.Count == 0)
        {
            GD.PrintErr("No destination activities set for the route.");
            return [];
        }

        return destinationActivities.ToArray();
    }

    public Dictionary<SupplyItemType, float> GetItemAndChance()
    {
        if (itemAndChance == null || itemAndChance.Count == 0)
        {
            GD.PrintErr("No items and chances set for the route.");
            return new Dictionary<SupplyItemType, float>();
        }

        return itemAndChance.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    public Dictionary<RelicItem, float> GetRelicChances()
    {
        if (relicChances == null || relicChances.Count == 0)
        {
            GD.PrintErr("No relic chances set for the route.");
            return new Dictionary<RelicItem, float>();
        }

        return relicChances.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    public string GetRouteName()
    {
        return translate.Tr(routeNameKey);
    }

    public string GetRouteDescription()
    {
        return translate.Tr(routeDescription);
    }

    public Texture2D GetRouteImage()
    {
        return routeImage;
    }

    public int GetNumberCamps()
    {
        return numberCamps;
    }

    public Vector2I GetStopRange()
    {
        return stopRange;
    }

    public Routes GetRouteType()
    {
        return routeType;
    }

    public static Route LoadRoute(Routes routeType)
    {
        string path = $"res://Application/Routes/{routeType}.tres";
        return ResourceLoader.Load<Route>(path);
    }
}

public interface IRoute
{
    Routes[]                          GetPossibleDestinations();
    IStop[]                           GetStopsAlongTheRoute();
    DestinationActivities[]           GetDestinationActivities();
    Dictionary<SupplyItemType, float> GetItemAndChance();
    Dictionary<RelicItem, float>      GetRelicChances();
    string                            GetRouteName();
    string                            GetRouteDescription();
    Texture2D                         GetRouteImage();
    int                               GetNumberCamps();
    Vector2I                          GetStopRange();
    Routes                            GetRouteType();
}

public enum Routes
{
    None,
    A,
    B,
    C,
}
using System.Reactive.Disposables;
using Godot;

namespace BringMeTreasure.Application;

public class Undo : IUndo
{
    private IDisposable undoAction;
    private string description;
    private Func<bool> undoImmediatelyFunc;

    public Undo(IDisposable undoAction, string desc, bool undoImmediately)
    {
        this.undoAction = undoAction;
        description = desc;
        undoImmediatelyFunc = () => undoImmediately;
    }

    public Undo(Action undoAction, string desc, bool undoImmediately = false)
    {
        this.undoAction = Disposable.Create(undoAction);
        description = desc;
        undoImmediatelyFunc = () => undoImmediately;
    }

    public Undo(Action undoAction, string desc, Func<bool> undoImmediately)
    {
        undoImmediatelyFunc = undoImmediately;
        this.undoAction = Disposable.Create(undoAction);
        description = desc;
    }

    public void UndoEffect()
    {
        undoAction.Dispose();
    }

    public bool GetIsUndoImmediately()
    {
        return undoImmediatelyFunc();
    }

    public string GetUndoDescription()
    {
        return description;
    }
}

public interface IUndo
{
    void UndoEffect();
    bool GetIsUndoImmediately();
    string GetUndoDescription();
}
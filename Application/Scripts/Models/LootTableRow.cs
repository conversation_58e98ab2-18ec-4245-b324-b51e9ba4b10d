using Godot;

namespace BringMeTreasure.Models;

[GlobalClass]
public partial class LootTableRow : Resource
{
    [Export] public SupplyItemType Item;
    [Export] public Vector2 QuantityRange;
    [Export] public Rarity Rarity;

    public LootTable.LootTableRowInstance Get()
    {
        return new LootTable.LootTableRowInstance(Item,
            new System.Numerics.Vector2(QuantityRange.X, QuantityRange.Y), Rarity);
    }
}
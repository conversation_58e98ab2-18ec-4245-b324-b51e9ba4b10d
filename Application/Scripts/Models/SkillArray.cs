using BringMeTreasure.SimpleStuff;
using Newtonsoft.Json;

namespace BringMeTreasure.Models;

[Serializable]
public class SkillArray
{
    private static Translate tr;
    private static Translate Tr => tr ??= new Translate();
    [JsonProperty("weightBySkillType")] Dictionary<SkillType, int> weightBySkillType = new();

    private Dictionary<SkillType, Skill> Skills
    {
        get
        {
            Dictionary<SkillType, Skill> dict = new();
            foreach (SkillType skill in Enum.GetValues<SkillType>())
            {
                dict.Add(skill, SkillToKeyValuePair(skill));
            }
            return dict;
        }
    }

    private Skill SkillToKeyValuePair(SkillType skill)
    {
        weightBySkillType.TryAdd(skill, 0);

        return new Skill(
            Tr.Tr(skill.ToString()),
            Tr.Tr(skill.ToString() + "_desc"),
            weightBySkillType[skill],
            skill);
    }
    
    public ISkill[] GetSkills()
    {
        Dictionary<SkillType, Skill> skills = Skills;
        return Enum.GetValues<SkillType>().Select(s=> (ISkill) skills[s])
            .ToArray();
    }
    
    public void SetWeight(SkillType skillType, int weight)
    {
        weightBySkillType[skillType] = weight;
    }
}

public enum SkillType
{
    Trapping,
    Tracking,
    Strength,
    Harvesting,
    Cooking,
    Fishing,
    Foraging,
    Scavenging,
}

public struct Skill(string name, string desc, int weight, SkillType type) : ISkill
{
    public string GetName()
    {
        return name;
    }

    public string GetDescription()
    {
        return desc;
    }

    public int GetWeight()
    {
        return weight;
    }

    public SkillType GetSkillType()
    {
        return type;
    }
}

[JsonObject(MemberSerialization.Fields)]
public struct SkillBonus(SkillType skillType, int weight)
{
    public SkillType SkillType = skillType;
    public int Weight = weight;
}

public interface ISkill : IAbility
{
    int GetWeight();
    SkillType GetSkillType();
    
}
using BringMeTreasure.Behaviors;
using Godot;

namespace BringMeTreasure.Models.MonsterAbilities;

[GlobalClass]
public partial class SlimeBall : MonsterAbility
{
    [Export] public PackedScene AttackAnimation;

    public override async Task RunAttackAnimation(Node2D attackOrigin, Node2D attackTarget)
    {
        SceneTree tree = Engine.GetMainLoop() as SceneTree;
        Node projectile = AttackAnimation.Instantiate();
        Node2D projectileNode = projectile as Node2D;
        tree.Root.AddChild(projectileNode);
        projectileNode.GlobalPosition = attackOrigin.GlobalPosition;// node disposed
        
        float distance = attackOrigin.GlobalPosition.DistanceTo(attackTarget.GlobalPosition);
        
        await Projectile.DirectLaunch(attackOrigin, attackTarget, projectileNode, distance / 1000f);
        projectile.QueueFree();
    }

    public override bool IsRanged()
    {
        return true;
    }
}
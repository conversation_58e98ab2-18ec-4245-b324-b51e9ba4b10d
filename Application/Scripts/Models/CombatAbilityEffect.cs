using BringMeTreasure.Application;
using BringMeTreasure.Battle;
using BringMeTreasure.Battle.ReticuleType;
using Godot;

namespace BringMeTreasure.Models;

public abstract class CombatAbilityEffect : ICombatAbilityEffect
{
    protected ICreatureController Target;
    protected bool IsApplied;

    public abstract void UndoEffect();

    public bool GetIsUndoImmediately()
    {
        return true;
    }

    public abstract string GetUndoDescription();

    public abstract string GetEffectDescription();
    public abstract bool GetTargetsSelf();
    public abstract bool GetTargetsEnemy();
    public abstract ReticuleType GetReticuleType();
    public abstract bool GetTargetsTeam();
    public abstract Color GetOutlineColor();
    public abstract ICombatAbilityEffect ApplyTo(ICreatureController t);
}

public interface ICombatAbilityEffect : IUndo
{
    string GetEffectDescription();
    bool GetTargetsSelf();
    bool GetTargetsEnemy();
    ReticuleType GetReticuleType();
    bool GetTargetsTeam();
    Color GetOutlineColor();
    ICombatAbilityEffect ApplyTo(ICreatureController t);
}
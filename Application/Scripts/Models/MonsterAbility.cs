using Godot;

namespace BringMeTreasure.Models;

public abstract partial class MonsterAbility : Resource, IMonsterAbility
{
    [Export] public string Name;
    [Export] public int Damage;
    [Export] public int PositionDamage;
    [Export] public int ArmorOnlyDamage;
    [Export] public string Description;
    public int GetDamage()
    {
        return Damage;
    }

    public int GetArmorOnlyDamage()
    {
        return ArmorOnlyDamage;
    }

    public int GetPositionDamage()
    {
        return PositionDamage;
    }

    public abstract Task RunAttackAnimation(Node2D attackOrigin, Node2D attackTarget);
    public abstract bool IsRanged();
}

public interface IMonsterAbility
{
    int GetDamage();
    int GetArmorOnlyDamage();
    int GetPositionDamage();
    Task RunAttackAnimation(Node2D attackOrigin, Node2D attackTarget);
    bool IsRanged();
}
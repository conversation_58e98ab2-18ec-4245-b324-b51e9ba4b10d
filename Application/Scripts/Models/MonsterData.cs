using BringMeTreasure.Battle;
using Godot;

namespace BringMeTreasure.Models;

public abstract partial class MonsterData : Resource, IMonsterData
{
    [Export] string name;
    [Export] Texture2D texture;
    [Export] Godot.Collections.Dictionary<MonsterAbility, float> abilities = new();
    [Export] Godot.Collections.Dictionary<SupplyItemResource, Vector2> parts = new();
    [Export] LootTable lootTable;
    [Export] int healthPoints;
    [Export] int positionPoints;
    [Export] int armorPoints;

    public Texture2D GetTexture()
    {
        return texture;
    }

    public IEnumerable<IMonsterAbility> GetAbilities()
    {
        return abilities.Keys;
    }

    public int GetHealthPoints()
    {
        return healthPoints;
    }

    public int GetPositionPoints()
    {
        return positionPoints;
    }

    public int GetArmorPoints()
    {
        return armorPoints;
    }

    public IMonsterAbility GetAttack()
    {
        if (abilities.Count == 0)
        {
            return null;
        }

        //select an ability based on chance
        float totalChance = abilities.Values.Sum(s => s);
        float randomValue = GD.Randf() * totalChance;

        float cumulativeChance = 0;
        foreach (var abilityAndChance in abilities)
        {
            cumulativeChance += abilityAndChance.Value;
            if (randomValue <= cumulativeChance)
            {
                return abilityAndChance.Key;
            }
        }

        return null; // Should not reach here if chances are set correctly
    }

    public abstract Task PlayRangedAttackAnimation(IMonster implementation);
    public ILootTable GetLootTable()
    {
        return lootTable;
    }
}

public interface IMonsterData
{
    string                       GetName();
    Texture2D                    GetTexture();
    IEnumerable<IMonsterAbility> GetAbilities();
    int                          GetHealthPoints();
    int                          GetPositionPoints();
    int                          GetArmorPoints();
    IMonsterAbility              GetAttack();
    Task                         PlayRangedAttackAnimation(IMonster implementation);
    ILootTable                   GetLootTable();
}

public class AbilityAndChance
{
    [Export] public MonsterAbility Ability;
    [Export] public float Chance;
}
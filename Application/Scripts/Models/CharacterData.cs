using BringMeTreasure.Models;
using Godot;
using Newtonsoft.Json;

namespace BringMeTreasure.Scripts.Characters;

[JsonObject(MemberSerialization.Fields)]
public abstract class CharacterData : ICharacterData
{
    protected int CurrentHealth = 12;
    protected int CurrentArmor = 4;
    protected int Positioning = 8;
    protected SkillArray SkillArray = new();
    protected IAbilityProviderItem[] quickSlotItems = new IAbilityProviderItem[3];
    protected Dictionary<ItemType, IAbilityProviderItem> equipment = new();
    public abstract string PortraitPath { get; }
    public abstract int MaxHealth { get; }


    public abstract ICombatAbility[] CombatAbilities();
    public abstract ITalent[] Talents();

    public ICombatAbility[] GetCombatAbilities()
    {
        List<ICombatAbility> total = new List<ICombatAbility>();
        total.AddRange(GetCombatAbilitiesFromEquipment());
        total.AddRange(CombatAbilities());
        return total.ToArray();
    }

    public ITalent[] GetTalents()
    {
        List<ITalent> total = new List<ITalent>();
        total.AddRange(GetTalentsFromEquipment());
        total.AddRange(Talents());
        return total.ToArray();
    }

    protected ICombatAbility[] GetCombatAbilitiesFromEquipment()
    {
        return equipment.Values
            .SelectMany(item => item.GetCombatAbilities())
            .ToArray();
    }

    protected ITalent[] GetTalentsFromEquipment()
    {
        return equipment.Values
            .SelectMany(item => item.GetTalents())
            .ToArray();
    }

    public ISkill[] GetSkills()
    {
        SkillBonus[] skillBonuses = equipment.Values
            .SelectMany(item => item.GetSkillBonuses())
            .ToArray();

        ISkill[] skills = SkillArray.GetSkills();

        List<ISkill> withBonus = new List<ISkill>();
        foreach (ISkill skill in skills)
        {
            SkillBonus[] bonuses = skillBonuses
                .Where(b => b.SkillType == skill.GetSkillType())
                .ToArray();

            Skill afterAddingBonuses = new Skill(skill.GetName(), skill.GetDescription(),
                skill.GetWeight() + bonuses.Sum(b => b.Weight), skill.GetSkillType());
            withBonus.Add(afterAddingBonuses);
        }


        return withBonus.ToArray();
    }

    public Texture2D GetTexture()
    {
        return GD.Load<Texture2D>(PortraitPath);
    }

    public int GetHealthPoints()
    {
        return CurrentHealth;
    }

    public int GetArmorPoints()
    {
        return CurrentArmor;
    }

    public int GetPositioningPoints()
    {
        return Positioning;
    }

    public int GetMaxHealth()
    {
        return MaxHealth;
    }

    public abstract string GetName();

    public void SetEquipment(ItemType itemType, IAbilityProviderItem item,  int objQuickSlotIndex)
    {
        switch (itemType)
        {
            case ItemType.Helmet:
            case ItemType.Bracers:
            case ItemType.Greaves:
            case ItemType.Tunic:
            case ItemType.BackPack:
            case ItemType.Weapon:
                equipment[itemType] = item;
                break;
            case ItemType.QuickItem:
                quickSlotItems[objQuickSlotIndex] = item;
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public IAbilityProviderItem GetEquipment(ItemType itemType, int objQuickSlotIndex)
    {
        if (itemType == ItemType.QuickItem)
        {
            if (objQuickSlotIndex >= quickSlotItems.Length)
            {
                throw new ArgumentOutOfRangeException(nameof(objQuickSlotIndex), "Invalid quick slot index.");
            }

            return quickSlotItems[objQuickSlotIndex];
        }
        
        return equipment.GetValueOrDefault(itemType);
    }

    public void RemoveEquipment(ItemType objItemType, int objQuickSlotIndex)
    {
        switch (objItemType)
        {
            case ItemType.Helmet:
            case ItemType.Bracers:
            case ItemType.Greaves:
            case ItemType.Tunic:
            case ItemType.BackPack:
            case ItemType.Weapon:
                equipment.Remove(objItemType);
                break;
            case ItemType.QuickItem:
                if (objQuickSlotIndex >= quickSlotItems.Length)
                {
                    throw new ArgumentOutOfRangeException(nameof(objQuickSlotIndex), "Invalid quick slot index.");
                }

                quickSlotItems[objQuickSlotIndex] = null;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(objItemType), "Invalid item type.");
        }
    }
}

public interface ICharacterData
{
    ICombatAbility[]     GetCombatAbilities();
    ITalent[]            GetTalents();
    ISkill[]             GetSkills();
    Texture2D            GetTexture();
    int                  GetHealthPoints();
    int                  GetArmorPoints();
    int                  GetPositioningPoints();
    int                  GetMaxHealth();
    int                  GetMaxArmor() => 25;
    string               GetName();
    void                 SetEquipment(ItemType itemType, IAbilityProviderItem item, int objQuickSlotIndex);
    IAbilityProviderItem GetEquipment(ItemType itemType, int objQuickSlotIndex);
    void                 RemoveEquipment(ItemType objItemType, int objQuickSlotIndex);
}
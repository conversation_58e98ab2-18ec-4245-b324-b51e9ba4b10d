using BringMeTreasure.Battle;
using BringMeTreasure.Battle.ReticuleType;
using Godot;

namespace BringMeTreasure.Models;

public class ArmorBoostEffect(int amount) : CombatAbilityEffect, IArmorBoostEffect
{
    public override void UndoEffect()
    {
        Target.TakeArmorDamage(amount);
    }

    public override string GetUndoDescription()
    {
        return "undoing armor boost effect";
    }

    public override string GetEffectDescription()
    {
        return $"Boosts the target's armor by {amount} for the duration of the effect.";
    }

    public override bool GetTargetsSelf()
    {
        return false; // This effect does not target self
    }

    public override bool GetTargetsEnemy()
    {
        return false; // This effect does not target enemies
    }

    public override ReticuleType GetReticuleType()
    {
        return ReticuleType.AttackEnemy; // todo change this
    }

    public override bool GetTargetsTeam()
    {
        return true; // This effect targets team members
    }

    public override Color GetOutlineColor()
    {
        return Colors.Green; // Color for the outline when targeting
    }

    public override ICombatAbilityEffect ApplyTo(ICreatureController t)
    {
        ArmorBoostEffect effect = new(amount);
        effect.Target = t;
        t.GainArmor(amount);
        effect.IsApplied = true;
        return effect;
    }
}

public interface IArmorBoostEffect : ICombatAbilityEffect
{
}
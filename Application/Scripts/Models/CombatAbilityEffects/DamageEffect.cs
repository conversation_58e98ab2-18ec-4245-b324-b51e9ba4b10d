using BringMeTreasure.Battle;
using BringMeTreasure.Battle.ReticuleType;
using Godot;

namespace BringMeTreasure.Models;

public class DamageEffect : CombatAbilityEffect, IDamageEffect
{
    private int damageToApply;

    private int armorDamageDone = 0;
    private int healthDamageDone = 0;

    public DamageEffect(int damageAmount)
    {
        damageToApply = damageAmount;
    }


    private void ApplyDamage(ICreatureController t)
    {
        IsApplied = true;
        Target = t;
        armorDamageDone = Target.TakeArmorDamage(damageToApply);
        int remainingDamage = damageToApply - armorDamageDone;
        if (remainingDamage > 0)
        {
            healthDamageDone = Target.TakeHealthDamage(remainingDamage);
        }
    }


    public override void UndoEffect()
    {
        if (!IsApplied)
        {
            return;
        }

        IsApplied = false;
        Target.UndoArmorDamage(armorDamageDone);
        Target.UndoHealthDamage(healthDamageDone);
    }

    public override string GetUndoDescription()
    {
        return $"undoing damage effect of {damageToApply} damage";
    }

    public override string GetEffectDescription()
    {
        return "Deals damage to the targeted creature.";
    }

    public override bool GetTargetsSelf()
    {
        return false;
    }

    public override bool GetTargetsEnemy()
    {
        return true;
    }

    public override ReticuleType GetReticuleType()
    {
        return ReticuleType.AttackEnemy;
    }

    public override bool GetTargetsTeam()
    {
        return false;
    }

    public override Color GetOutlineColor()
    {
        return new Color(1, 0, 0); // Red color for damage
    }

    public override ICombatAbilityEffect ApplyTo(ICreatureController t)
    {
        DamageEffect effect = new DamageEffect(damageToApply);
        effect.ApplyDamage(t);
        return effect;
    }

    public int GetDamage()
    {
        return damageToApply;
    }
}

public interface IDamageEffect : ICombatAbilityEffect
{
    int GetDamage();
}
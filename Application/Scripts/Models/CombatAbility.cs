using System.Text.Json.Serialization;
using BringMeTreasure.Battle;
using Godot;
using Newtonsoft.Json;

namespace BringMeTreasure.Models;

public abstract class CombatAbility : ICombatAbility
{
    [JsonProperty] protected string name;

    [JsonProperty] protected string description;

    [JsonProperty] protected int uses;

    [System.Text.Json.Serialization.JsonIgnore] // Prevent serialization of this field
    private IBattleCharacterController caster;

    public string GetName()
    {
        return name;
    }

    public string GetDescription()
    {
        return description;
    }

    public int GetUses()
    {
        return uses;
    }

    public abstract ICombatAbilityEffect[] GetEffects();

    public bool GetIsCostsAction()
    {
        return true;
    }

    public abstract RegainType GetRegainType();

    public void Use()
    {
        if (uses > 0)
        {
            uses--;
        }
    }
}

public interface ICombatAbility : IAbility
{
    int GetUses();
    ICombatAbilityEffect[] GetEffects();
    bool GetIsCostsAction();
    RegainType GetRegainType();

    /// <summary>
    /// Should reduce uses by 1
    /// </summary>
    void Use();
}
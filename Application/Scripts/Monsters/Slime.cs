using BringMeTreasure.Application.Scripts.Behaviors;
using BringMeTreasure.Battle;
using BringMeTreasure.Models;
using Godot;

namespace BringMeTreasure.Application.Scripts.Monsters;

[GlobalClass]
public partial class Slime : MonsterData
{
    public override async Task PlayRangedAttackAnimation(IMonster implementation)
    {
        implementation.StopAnimation();
        Control texture = implementation.GetTextureButton();
        bool completed = false;
        Tween tween = implementation.MakeTween();
        float originalY = texture.Scale.Y;
        tween.TweenProperty(texture, "scale:y", originalY * .6, 0.1f);
        tween.TweenInterval(.2f);
        tween.TweenProperty(texture, "scale:y", originalY, 0.1f);
        await Task.Delay(TimeSpan.FromSeconds(.4f));
    }
}
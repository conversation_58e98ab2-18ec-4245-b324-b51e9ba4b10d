using BringMeTreasure.Abilities;
using BringMeTreasure.Models;
using Newtonsoft.Json;

namespace BringMeTreasure.Application.Items.Relics;

[JsonObject(MemberSerialization.Fields)]
public class BasicAttackRelic : IAbilityProviderItem
{
    SwordAttack swordAttack;

    public BasicAttackRelic()
    {
        swordAttack = new SwordAttack("Basic Sword Attack",
            "A basic sword attack that deals damage to a single target.", 10, 4);
    }

    public string GetItemName()
    {
        return "Basic Attack Relic";
    }

    public string GetItemDescription()
    {
        return "A relic that enhances basic attack abilities.";
    }

    public string GetItemIconPath()
    {
        return "res://path/to/basic_attack_relic_icon.png";
    }

    public ItemType GetItemType()
    {
        return ItemType.Weapon;
    }

    public EffectAmount[] GetResistances()
    {
        return [];
    }

    public EffectAmount[] GetEffects()
    {
        return [];
    }

    public SkillBonus[] GetSkillBonuses()
    {
        return [new SkillBonus(SkillType.Fishing, 10)];
    }

    public ICombatAbility[] GetCombatAbilities()
    {
        return
        [
            swordAttack
        ];
    }

    public ITalent[] GetTalents()
    {
        return [new Heal(5, 10, RegainType.None)];
    }
}
using BringMeTreasure.Abilities;
using BringMeTreasure.Models;
using BringMeTreasure.SimpleStuff;
using Newtonsoft.Json;

namespace BringMeTreasure.Scripts.Characters;

[JsonObject(MemberSerialization.Fields)]
public class Raider : CharacterData
{
    private int level = 0;

    private List<CombatAbility> abilities;


    public override string PortraitPath => "res://Application/Characters/Raider/Raider.png";
    public override int MaxHealth => 12;

    public override CombatAbility[] CombatAbilities()
    {
        return abilities.ToArray();
    }

    public override ITalent[] Talents()
    {
        return new ITalent[]
        {
            new Heal(5, 5, RegainType.Rest)
        };
    }

    public override string GetName()
    {
        return new Translate().Tr("Raider");
    }

    public static Raider Create()
    {
        Raider raider = new();
        raider.level = 1;
        raider.abilities = new List<CombatAbility>();
        raider.abilities.Add(new SwordAttack("Sword AttackEnemy", "A basic sword attack.", 10, 4));
        raider.abilities.Add(new Fortify("Fortify", "Increases armor for the next turn.", 3, 2));
        return raider;
    }
}
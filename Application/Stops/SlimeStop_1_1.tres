[gd_resource type="Resource" script_class="Stop" load_steps=4 format=3 uid="uid://dwcuqetj4giuu"]

[ext_resource type="Script" uid="uid://b8tquocgutyak" path="res://Application/Scripts/Models/Route/Stop.cs" id="1_uappp"]
[ext_resource type="Resource" uid="uid://d32muo7p3b5hl" path="res://Application/Monsters/Slime/Slime.tres" id="1_xvev4"]
[ext_resource type="Resource" uid="uid://bjxyfgdjjhy87" path="res://Application/Monsters/Slime/Slime2.tres" id="2_k5iat"]

[resource]
script = ExtResource("1_uappp")
possibleMonsters = Array[Resource]([ExtResource("1_xvev4"), ExtResource("1_xvev4"), ExtResource("1_xvev4"), ExtResource("2_k5iat")])
metadata/_custom_type_script = "uid://b8tquocgutyak"

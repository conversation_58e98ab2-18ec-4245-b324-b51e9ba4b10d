using Godot;

namespace BringMeTreasure.Shaders;

// uniform bool allow_out_of_bounds = true;
// uniform float outline_thickness: hint_range(0.0, 16.0, 1.0) = 1.0;
// uniform vec4 outline_color: source_color = vec4(1.0);
public class GlowShaderController :IGlowShader
{
    private readonly ShaderMaterial shader;

    public GlowShaderController(ShaderMaterial shader)
    {
        this.shader = shader;
    }

    private int point;

    public void SetActive(bool b)
    {
        if (!b)
        {
            point = 0;
        }

        shader.SetShaderParameter("outline_thickness", point);
    }

    public void SetActiveAndThickness(int points)
    {
        point = points;
        shader.SetShaderParameter("outline_thickness", points);
    }

    public void SetColor(Color c)
    {
        if (c == default)
        {
            c = Colors.White;
        }

        shader.SetShaderParameter("outline_color", c);
    }
}

public interface IGlowShader
{
    public void SetActive(bool b);
    public void SetActiveAndThickness(int points);
    public void SetColor(Color c);
}